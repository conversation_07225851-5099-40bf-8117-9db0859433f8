<?php

namespace App\Controllers;

class DashboardController extends BaseController
{
    /**
     * Display the dashboard landing page (GET request)
     * 
     * @return string
     */
    public function index()
    {
        return view('dashboard/dashboard_landing');
    }

    /**
     * Handle dashboard data requests (GET request)
     * RESTful endpoint for dashboard data
     * 
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function getData()
    {
        // This method can be used for AJAX requests to get dashboard data
        $data = [
            'status' => 'success',
            'message' => 'Dashboard data retrieved successfully',
            'data' => [
                'active_workplans' => 24,
                'pending_claims' => 12,
                'total_users' => 156,
                'completion_rate' => 89
            ]
        ];

        return $this->response->setJSON($data);
    }

    /**
     * Handle dashboard form submissions (POST request)
     * 
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function store()
    {
        // Handle POST requests for dashboard actions
        $input = $this->request->getJSON(true) ?? $this->request->getPost();
        
        // Validate input
        if (empty($input)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'No data provided'
            ])->setStatusCode(400);
        }

        // Process the dashboard action based on input
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'update_preferences':
                return $this->updatePreferences($input);
            case 'refresh_stats':
                return $this->refreshStats();
            default:
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Invalid action specified'
                ])->setStatusCode(400);
        }
    }

    /**
     * Update user dashboard preferences (POST request)
     * 
     * @param array $input
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    private function updatePreferences($input)
    {
        // Handle dashboard preference updates
        $preferences = $input['preferences'] ?? [];
        
        // Here you would typically save to database
        // For now, return success response
        
        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Dashboard preferences updated successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Refresh dashboard statistics (POST request)
     * 
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    private function refreshStats()
    {
        // Refresh and return updated statistics
        $stats = [
            'active_workplans' => rand(20, 30),
            'pending_claims' => rand(10, 20),
            'total_users' => rand(150, 200),
            'completion_rate' => rand(80, 95)
        ];

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Dashboard statistics refreshed',
            'data' => $stats
        ]);
    }

    /**
     * Handle dashboard updates (PUT request)
     * 
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function update($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'ID is required for update'
            ])->setStatusCode(400);
        }

        $input = $this->request->getJSON(true) ?? $this->request->getPost();
        
        // Handle specific dashboard item updates
        return $this->response->setJSON([
            'status' => 'success',
            'message' => "Dashboard item {$id} updated successfully",
            'data' => $input
        ]);
    }

    /**
     * Delete dashboard item (DELETE request)
     * 
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function delete($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'ID is required for deletion'
            ])->setStatusCode(400);
        }

        // Handle dashboard item deletion
        return $this->response->setJSON([
            'status' => 'success',
            'message' => "Dashboard item {$id} deleted successfully"
        ]);
    }
}
