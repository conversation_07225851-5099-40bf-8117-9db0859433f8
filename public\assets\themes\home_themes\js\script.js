// IPMS Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {

    // Initialize all functionality
    initSmoothScrolling();
    initScrollAnimations();
    initNavbarEffects();
    initLoadingStates();
    initAccessibility();

    // Smooth scrolling for navigation links
    function initSmoothScrolling() {
        const navLinks = document.querySelectorAll('a[href^="#"]');

        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);

                if (targetSection) {
                    const navbarHeight = document.querySelector('.navbar').offsetHeight;
                    const targetPosition = targetSection.offsetTop - navbarHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Update active nav link
                    updateActiveNavLink(targetId);
                }
            });
        });
    }

    // Update active navigation link
    function updateActiveNavLink(targetId) {
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === targetId) {
                link.classList.add('active');
            }
        });
    }

    // Scroll-triggered animations
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');

                    // Animate feature cards with stagger effect
                    if (entry.target.classList.contains('feature-card') ||
                        entry.target.classList.contains('module-card')) {
                        animateCards(entry.target.parentElement);
                    }
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animatedElements = document.querySelectorAll('.feature-card, .module-card, .stat-item');
        animatedElements.forEach(el => {
            el.classList.add('fade-in');
            observer.observe(el);
        });
    }

    // Animate cards with stagger effect
    function animateCards(container) {
        const cards = container.querySelectorAll('.feature-card, .module-card');

        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Navbar effects on scroll
    function initNavbarEffects() {
        const navbar = document.querySelector('.navbar');
        let lastScrollTop = 0;

        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Add/remove navbar background on scroll
            if (scrollTop > 100) {
                navbar.style.backgroundColor = 'rgba(128, 0, 0, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.backgroundColor = '#800000';
                navbar.style.backdropFilter = 'none';
            }

            // Update active section in navigation
            updateActiveSection();

            lastScrollTop = scrollTop;
        });
    }

    // Update active section based on scroll position
    function updateActiveSection() {
        const sections = document.querySelectorAll('section[id]');
        const navbarHeight = document.querySelector('.navbar').offsetHeight;
        const scrollPosition = window.scrollY + navbarHeight + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = '#' + section.getAttribute('id');

            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                updateActiveNavLink(sectionId);
            }
        });
    }

    // Loading states for buttons
    function initLoadingStates() {
        const buttons = document.querySelectorAll('.btn');

        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Only add loading state for login buttons
                if (this.href && this.href.includes('home/login')) {
                    e.preventDefault();

                    const originalText = this.innerHTML;
                    this.innerHTML = '<span class="loading"></span> Loading...';
                    this.disabled = true;

                    // Simulate loading time
                    setTimeout(() => {
                        window.location.href = this.href;
                    }, 1000);
                }
            });
        });
    }

    // Accessibility enhancements
    function initAccessibility() {
        // Add keyboard navigation for cards
        const cards = document.querySelectorAll('.feature-card, .module-card');

        cards.forEach(card => {
            card.setAttribute('tabindex', '0');
            card.setAttribute('role', 'button');

            card.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // Add focus indicators
        const focusableElements = document.querySelectorAll('a, button, [tabindex]');

        focusableElements.forEach(element => {
            element.addEventListener('focus', function() {
                this.style.outline = '2px solid #28a745';
                this.style.outlineOffset = '2px';
            });

            element.addEventListener('blur', function() {
                this.style.outline = 'none';
            });
        });
    }

    // Parallax effect for hero section
    function initParallaxEffect() {
        const heroIcon = document.querySelector('.hero-icon');

        if (heroIcon) {
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = scrolled * 0.5;

                heroIcon.style.transform = `translateY(${parallax}px)`;
            });
        }
    }

    // Initialize parallax effect
    initParallaxEffect();

    // Mobile menu toggle enhancement
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            // Add animation class
            navbarCollapse.classList.toggle('show');
        });

        // Close mobile menu when clicking on a link
        const mobileNavLinks = document.querySelectorAll('.navbar-nav .nav-link');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth < 992) {
                    navbarCollapse.classList.remove('show');
                }
            });
        });
    }

    // Scroll to top functionality
    function createScrollToTopButton() {
        const scrollButton = document.createElement('button');
        scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        scrollButton.className = 'scroll-to-top';
        scrollButton.setAttribute('aria-label', 'Scroll to top');

        // Add styles
        scrollButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background-color: #800000;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;

        document.body.appendChild(scrollButton);

        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollButton.style.opacity = '1';
                scrollButton.style.visibility = 'visible';
            } else {
                scrollButton.style.opacity = '0';
                scrollButton.style.visibility = 'hidden';
            }
        });

        // Scroll to top when clicked
        scrollButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Hover effects
        scrollButton.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#28a745';
            this.style.transform = 'scale(1.1)';
        });

        scrollButton.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#800000';
            this.style.transform = 'scale(1)';
        });
    }

    // Initialize scroll to top button
    createScrollToTopButton();

    // Performance optimization: Throttle scroll events
    function throttle(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Apply throttling to scroll events
    const throttledScrollHandler = throttle(function() {
        updateActiveSection();
    }, 100);

    window.addEventListener('scroll', throttledScrollHandler);

    // Preload critical resources
    function preloadResources() {
        const criticalResources = [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = resource;
            document.head.appendChild(link);
        });
    }

    // Initialize resource preloading
    preloadResources();

    // Error handling for missing elements
    function handleMissingElements() {
        const requiredElements = ['.navbar', '.hero-section', '#features'];

        requiredElements.forEach(selector => {
            if (!document.querySelector(selector)) {
                console.warn(`Required element ${selector} not found`);
            }
        });
    }

    // Check for missing elements
    handleMissingElements();

    // Add loading complete class to body
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');

        // Trigger any final animations
        const heroElements = document.querySelectorAll('.hero-section *');
        heroElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });

    console.log('IPMS Landing Page initialized successfully');
});