# IPMS Landing Page

## Overview

This is the professional landing page for the **Integrated Progress Monitoring System (IPMS)**, designed to provide a clean, modern, and responsive introduction to the system. The landing page follows the project's design guidelines with a green (#28a745) primary color and maroon (#800000) secondary color scheme.

## File Structure

```
app/Views/home/
├── home_index.php          # Main landing page (renamed with home_ prefix)
├── home_login.php          # Login page (renamed with home_ prefix)
├── home_dashboard.php      # Demo dashboard (renamed with home_ prefix)
└── README.md               # This documentation

public/assets/themes/home_themes/
├── css/
│   └── style.css           # Custom styles (moved from app/Views/home/<USER>/)
└── js/
    └── script.js           # Interactive functionality (moved from app/Views/home/<USER>/)

public/assets/system_images/
├── dakoii-systems-logo.png # Dakoii Systems company logo
├── ipms-icon.png           # IPMS system icon
├── ipms_system_logo.png    # IPMS system logo
└── favicon.ico             # Website favicon
```

## Features

### Landing Page (home_index.php)

#### Sections:
1. **Hero Section**
   - Gradient background with green primary color scheme
   - Animated floating chart icon with IPMS branding
   - Call-to-action buttons (Get Started, Learn More)
   - Responsive design for all devices

2. **Key Features Section**
   - 6 feature cards with hover animations
   - Icons representing each major system feature
   - Clean card-based layout

3. **About Section**
   - System overview and benefits
   - Statistics grid with animated icons
   - Two-column responsive layout

4. **System Modules Section**
   - 8 module cards showcasing system capabilities
   - Hover effects and professional styling
   - Grid layout that adapts to screen size

5. **Strategic Planning Alignment Section**
   - Visual hierarchy showing connection from Vision 2050 to workplans
   - Detailed explanation of MTDP IV integration
   - Professional layout with maroon accents

6. **Contact/CTA Section**
   - Final call-to-action with login button
   - Green background with maroon accent buttons

7. **Professional Footer**
   - Dakoii Systems branding and development credits
   - System information and copyright
   - Version details and company website link

#### Interactive Features:
- Smooth scrolling navigation
- Scroll-triggered animations
- Active section highlighting in navigation
- Mobile-responsive hamburger menu
- Scroll-to-top button
- Loading states for buttons
- Accessibility enhancements

### Login Page (home_login.php)

#### Features:
- Professional login form with floating labels
- Green gradient background matching landing page
- Animated floating shapes with IPMS icons
- Demo credentials for testing
- Forgot password modal with green branding
- Form validation and error handling
- Loading states and success/error alerts
- Back to home navigation

#### Demo Credentials:
- **Email:** <EMAIL>
- **Password:** admin123

## Design Specifications

### Color Scheme
- **Primary (Green):** #28a745 - Navigation, headings, primary actions
- **Secondary (Maroon):** #800000 - Buttons, accents, borders
- **Light Background:** #f8f9fa
- **Dark Text:** #343a40
- **Muted Text:** #6c757d
- **White:** #ffffff

### Typography
- **Font Family:** System fonts (-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto)
- **Headings:** Bold, maroon-colored
- **Body Text:** Dark gray for readability
- **Links:** Green for interactive elements

### Responsive Breakpoints
- **Desktop:** 1200px and above
- **Tablet:** 768px - 1199px
- **Mobile:** Below 768px

## Technical Implementation

### Dependencies
- **Bootstrap 5.3.0:** Responsive framework and components
- **Font Awesome 6.4.0:** Icons and visual elements
- **Custom CSS:** Brand-specific styling and animations
- **Vanilla JavaScript:** Interactive functionality

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Performance Features
- Optimized CSS with CSS variables
- Throttled scroll events
- Intersection Observer for animations
- Preloaded critical resources
- Minified external dependencies via CDN

## Usage Instructions

### Setup
1. Files are properly located in the `app/Views/home/<USER>
2. Theme assets moved to `public/assets/themes/home_themes/` directory
3. System images located in `public/assets/system_images/` directory
4. Ensure web server has PHP support and CodeIgniter 4 is properly configured
5. Access via the root URL `/` or `/home` for the landing page
6. Access via `/home/<USER>
7. Access via `/home/<USER>

### Customization
1. **Colors:** Modify CSS variables in `public/assets/themes/home_themes/css/style.css`
2. **Content:** Edit text content in `app/Views/home/<USER>
3. **Features:** Add/remove feature cards as needed
4. **Modules:** Update system modules section for new features
5. **Images:** Replace system images in `public/assets/system_images/` directory
6. **Branding:** Update Dakoii Systems references and links as needed

### Integration
- Update login form action to point to actual authentication endpoint
- Replace demo credentials with real authentication system
- Add actual dashboard redirect URL
- Implement forgot password functionality

## Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Focus indicators
- Screen reader friendly
- High contrast color scheme
- Responsive text sizing

## SEO Optimization

- Semantic HTML5 structure
- Meta viewport tag for mobile
- Descriptive page titles
- Clean URL structure
- Fast loading times
- Mobile-first responsive design

## Security Considerations

- Form validation (client and server-side needed)
- CSRF protection (to be implemented)
- Input sanitization (to be implemented)
- Secure password handling (to be implemented)
- HTTPS enforcement (recommended)

## Future Enhancements

1. **Content Management**
   - Admin panel for content updates
   - Dynamic feature management
   - Multi-language support

2. **Analytics**
   - Google Analytics integration
   - User interaction tracking
   - Performance monitoring

3. **Advanced Features**
   - Dark mode toggle
   - Advanced animations
   - Progressive Web App features
   - Offline functionality

## Maintenance

### Regular Updates
- Keep Bootstrap and Font Awesome updated
- Monitor browser compatibility
- Update content as system evolves
- Optimize performance regularly

### Testing
- Cross-browser testing
- Mobile device testing
- Accessibility testing
- Performance testing
- Form functionality testing

## Support

For technical support or questions about the landing page:
1. Check browser console for JavaScript errors
2. Validate HTML and CSS
3. Test on different devices and browsers
4. Review this documentation

## Version History

- **v1.0** - Initial release with complete landing page and login functionality
- Professional design with IPMS branding and green/maroon color scheme
- Responsive layout for all devices
- Interactive features and animations
- Accessibility compliance
- Integration with system images and Dakoii Systems branding

## Development Credits

**Developed and Powered by Dakoii Systems**
- Website: [www.dakoiims.com](http://www.dakoiims.com)
- Professional software development and system integration services
- Specializing in progress monitoring and management systems

## System Images

The following system images are included:
- **IPMS Icon:** `public/assets/system_images/ipms-icon.png`
- **IPMS Logo:** `public/assets/system_images/ipms_system_logo.png`
- **Dakoii Systems Logo:** `public/assets/system_images/dakoii-systems-logo.png`
- **Favicon:** `public/assets/system_images/favicon.ico`

---

**Note:** This landing page is designed to integrate with the full IPMS system built on CodeIgniter 4. All view files follow the `home_` prefix naming convention and theme assets are organized in the `public/assets/themes/home_themes/` directory.