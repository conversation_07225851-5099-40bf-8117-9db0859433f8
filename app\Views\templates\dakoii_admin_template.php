<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->renderSection('title') ?> - Dakoii Systems</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">

    <!-- Bootstrap 5 CSS (Dark Theme) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --dakoii-dark-green: #1a4d3a;
            --dakoii-green: #28a745;
            --dakoii-light-green: #34ce57;
            --dakoii-dark-maroon: #4a0e0e;
            --dakoii-maroon: #800000;
            --dakoii-light-maroon: #a00000;
            --dakoii-bg-primary: #0d1117;
            --dakoii-bg-secondary: #161b22;
            --dakoii-bg-tertiary: #21262d;
            --dakoii-border: #30363d;
            --dakoii-text-primary: #f0f6fc;
            --dakoii-text-secondary: #8b949e;
        }

        body {
            background-color: var(--dakoii-bg-primary);
            color: var(--dakoii-text-primary);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, var(--dakoii-bg-secondary) 0%, var(--dakoii-bg-tertiary) 100%);
            border-right: 1px solid var(--dakoii-border);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--dakoii-border);
            background: linear-gradient(135deg, var(--dakoii-dark-green), var(--dakoii-green));
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .sidebar-brand img {
            margin-right: 0.75rem;
        }

        .sidebar.collapsed .sidebar-brand-text {
            display: none;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: var(--dakoii-text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--dakoii-bg-tertiary);
            color: var(--dakoii-green);
            border-left-color: var(--dakoii-green);
        }

        .nav-link.active {
            background-color: var(--dakoii-bg-tertiary);
            color: var(--dakoii-green);
            border-left-color: var(--dakoii-green);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        .sidebar.collapsed .nav-link-text {
            display: none;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        /* Top Navigation */
        .top-navbar {
            background: var(--dakoii-bg-secondary);
            border-bottom: 1px solid var(--dakoii-border);
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--dakoii-text-primary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background-color: var(--dakoii-bg-tertiary);
            color: var(--dakoii-green);
        }

        /* Content Area */
        .content-area {
            padding: 2rem;
        }

        /* Cards */
        .card {
            background-color: var(--dakoii-bg-secondary);
            border: 1px solid var(--dakoii-border);
            border-radius: 0.75rem;
        }

        .card-header {
            background-color: var(--dakoii-bg-tertiary);
            border-bottom: 1px solid var(--dakoii-border);
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        /* Buttons */
        .btn-dakoii-primary {
            background: linear-gradient(135deg, var(--dakoii-green), var(--dakoii-light-green));
            border: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-dakoii-primary:hover {
            background: linear-gradient(135deg, var(--dakoii-dark-green), var(--dakoii-green));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn-dakoii-secondary {
            background: linear-gradient(135deg, var(--dakoii-maroon), var(--dakoii-light-maroon));
            border: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-dakoii-secondary:hover {
            background: linear-gradient(135deg, var(--dakoii-dark-maroon), var(--dakoii-maroon));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(128, 0, 0, 0.3);
        }

        /* Forms */
        .form-control {
            background-color: var(--dakoii-bg-tertiary);
            border: 1px solid var(--dakoii-border);
            color: var(--dakoii-text-primary);
        }

        .form-control:focus {
            background-color: var(--dakoii-bg-tertiary);
            border-color: var(--dakoii-green);
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            color: var(--dakoii-text-primary);
        }

        .form-label {
            color: var(--dakoii-text-primary);
            font-weight: 500;
        }

        /* Tables */
        .table-dark {
            --bs-table-bg: var(--dakoii-bg-secondary);
            --bs-table-border-color: var(--dakoii-border);
        }

        /* Badges */
        .badge-success {
            background-color: var(--dakoii-green);
        }

        .badge-danger {
            background-color: var(--dakoii-maroon);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 0;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--dakoii-bg-primary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--dakoii-border);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--dakoii-green);
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Status indicators */
        .status-online {
            color: var(--dakoii-green);
        }

        .status-offline {
            color: var(--dakoii-text-secondary);
        }

        .status-error {
            color: var(--dakoii-maroon);
        }
    </style>

    <?= $this->renderSection('styles') ?>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="<?= site_url('dakoii/dashboard') ?>" class="sidebar-brand">
                <img src="<?= base_url('public/assets/system_images/dakoii-systems-logo.png') ?>" alt="Dakoii" height="32">
                <span class="sidebar-brand-text">Dakoii Systems</span>
            </a>
        </div>

        <ul class="sidebar-nav list-unstyled">
            <li class="nav-item">
                <a href="<?= site_url('dakoii/dashboard') ?>" class="nav-link <?= (uri_string() == 'dakoii/dashboard') ? 'active' : '' ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="nav-link-text">Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= site_url('dakoii/organizations') ?>" class="nav-link <?= (strpos(uri_string(), 'organizations') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-building"></i>
                    <span class="nav-link-text">Organizations</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= site_url('dakoii/admins') ?>" class="nav-link <?= (strpos(uri_string(), 'admins') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-users-cog"></i>
                    <span class="nav-link-text">Organization Admins</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= site_url('dakoii/settings') ?>" class="nav-link <?= (strpos(uri_string(), 'settings') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-cog"></i>
                    <span class="nav-link-text">Settings</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= site_url('dakoii/reports') ?>" class="nav-link <?= (strpos(uri_string(), 'reports') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-chart-bar"></i>
                    <span class="nav-link-text">Reports</span>
                </a>
            </li>
        </ul>

        <div class="mt-auto p-3 border-top border-secondary">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                        <i class="fas fa-user text-white"></i>
                    </div>
                </div>
                <div class="flex-grow-1 ms-2 sidebar-brand-text">
                    <div class="small text-light">Dakoii Admin</div>
                    <div class="small text-muted">Super Administrator</div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h4 class="mb-0 ms-3"><?= $this->renderSection('page_title') ?></h4>
            </div>

            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>Admin
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= site_url('dakoii') ?>"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?= $this->renderSection('content') ?>
        </div>
    </main>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarToggle = document.getElementById('sidebarToggle');

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });

            // Mobile sidebar toggle
            if (window.innerWidth <= 768) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Auto-collapse on mobile
            window.addEventListener('resize', function() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('collapsed');
                    mainContent.classList.remove('expanded');
                }
            });
        });
    </script>

    <?= $this->renderSection('scripts') ?>
</body>
</html>
