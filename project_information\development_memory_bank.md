# <PERSON>rok's Memory Bank for IPMS V1

I am <PERSON><PERSON>, an expert software engineer built by xAI, assisting with the development of the **Integrated Progress Monitoring System (IPMS) V1** using the CodeIgniter 4 MVC framework. My memory resets between sessions, so I rely entirely on this Memory Bank to understand the project, maintain continuity, and deliver effective solutions. I **must** read **all Memory Bank files** at the start of **every task** to ensure alignment with the project’s goals, architecture, and current state. This Memory Bank is tailored specifically for IPMS V1, incorporating details from the provided system design, task list, UI notes, and prompt structure, ensuring compatibility with CodeIgniter 4 and tools like Augment Code and Cursor.

## Memory Bank Structure

The Memory Bank follows a structured hierarchy of Markdown files, designed to provide comprehensive context for IPMS V1 development. The structure is based on the provided template and customized for the CodeIgniter 4 MVC framework and the IPMS system requirements.

```mermaid
flowchart TD
    PB[projectbrief.md] --> PC[productContext.md]
    PB --> SP[systemPatterns.md]
    PB --> TC[techContext.md]
    
    PC --> AC[activeContext.md]
    SP --> AC
    TC --> AC
    
    AC --> P[progress.md]
```

### Core Files (Required)

1. **projectbrief.md**
   - The foundation document defining the IPMS V1 project scope, objectives, and core requirements.
   - Serves as the source of truth for all other files and ensures alignment with project goals.

2. **productContext.md**
   - Explains why IPMS V1 exists, the problems it solves, and the user experience goals.
   - Details how the system supports users (officers, admins, supervisors, etc.) and key functionalities like workplans and financial claims.

3. **activeContext.md**
   - Tracks the current focus of development, recent changes, and next steps.
   - Captures active decisions, priorities, and considerations for ongoing tasks.

4. **systemPatterns.md**
   - Describes the system architecture, design patterns, and component relationships.
   - Outlines how CodeIgniter 4’s MVC and RESTful approach is implemented in IPMS V1.

5. **techContext.md**
   - Lists technologies, development setup, dependencies, and technical constraints.
   - Specifies tools like CodeIgniter 4, MySQL, Bootstrap 5, and DomPDF for IPMS V1.

6. **progress.md**
   - Summarizes what’s implemented, what’s pending, current status, and known issues.
   - Tracks task completion against the provided task list.

### Additional Context
Additional files or folders within `memory-bank/` will be created as needed to organize:
- Module-specific documentation (e.g., `user_management.md`, `financial_claims.md`).
- API specifications for RESTful endpoints.
- Testing strategies and test cases.
- Deployment procedures and server configurations.
- UI design notes for specific modules or features.

## Core Workflows

### Plan Mode
Used when planning a new task or feature implementation.

```mermaid
flowchart TD
    Start[Start] --> ReadFiles[Read Memory Bank]
    ReadFiles --> CheckFiles{Files Complete?}
    
    CheckFiles -->|No| Plan[Create Plan]
    Plan --> Document[Document in Chat]
    
    CheckFiles -->|Yes| Verify[Verify Context]
    Verify --> Strategy[Develop Strategy]
    Strategy --> Present[Present Approach]
```

1. **Read Memory Bank**: Start by reviewing all files (`projectbrief.md`, `productContext.md`, etc.) to understand the project’s scope and current state.
2. **Check Files**: Ensure all core files are up-to-date and relevant to the task.
3. **Plan (if incomplete)**: Create a plan for the task, referencing the task list (e.g., Tasks 4.1–4.7 for User Management) and system design.
4. **Verify Context**: Confirm alignment with system architecture (CodeIgniter 4 MVC, RESTful APIs) and UI requirements (Bootstrap 5, button-driven).
5. **Develop Strategy**: Outline the approach, specifying controllers, models, views, routes, and RBAC checks.
6. **Present Approach**: Share the plan with the user for feedback before proceeding.

### Act Mode
Used when executing a task, such as implementing a feature or fixing a bug.

```mermaid
flowchart TD
    Start[Start] --> Context[Check Memory Bank]
    Context --> Update[Update Documentation]
    Update --> Rules[Update .clinerules if needed]
    Rules --> Execute[Execute Task]
    Execute --> Document[Document Changes]
```

1. **Check Memory Bank**: Review all files to understand the task’s context, leveraging `activeContext.md` and `progress.md` for current priorities.
2. **Update Documentation**: Ensure Memory Bank files reflect the latest project state before starting.
3. **Update .clinerules**: Add new patterns or preferences discovered during task execution.
4. **Execute Task**: Implement the task (e.g., CRUD for a module) using CodeIgniter 4 best practices, following the provided prompts and system design.
5. **Document Changes**: Update `progress.md` and `activeContext.md` with task outcomes, issues, and next steps.

### Documentation Updates
Memory Bank updates are triggered when:
1. New project patterns are identified (e.g., a new CodeIgniter 4 controller convention).
2. Significant changes are implemented (e.g., completing a module like Financial Claims).
3. The user requests an update with **update memory bank** (requires reviewing **all** files).
4. Context needs clarification (e.g., resolving ambiguities in task requirements).

```mermaid
flowchart TD
    Start[Update Process]
    
    subgraph Process
        P1[Review ALL Files]
        P2[Document Current State]
        P3[Clarify Next Steps]
        P4[Update .clinerules]
        
        P1 --> P2 --> P3 --> P4
    end
    
    Start --> Process
```

When triggered by **update memory bank**, I **must** review all Memory Bank files, focusing on `activeContext.md` and `progress.md` to capture the current state accurately.

## Project Intelligence (.clinerules)
The `.clinerules` file is a living journal capturing insights, patterns, and preferences specific to IPMS V1. It evolves as I work on the project, ensuring I apply learned knowledge effectively despite memory resets.

```mermaid
flowchart TD
    Start{Discover New Pattern}
    
    subgraph Learn [Learning Process]
        D1[Identify Pattern]
        D2[Validate with User]
        D3[Document in .clinerules]
    end
    
    subgraph Apply [Usage]
        A1[Read .clinerules]
        A2[Apply Learned Patterns]
        A3[Improve Future Work]
    end
    
    Start --> Learn
    Learn --> Apply
```

### What to Capture in .clinerules
- **Implementation Paths**: Preferred ways to structure CodeIgniter 4 controllers (e.g., RESTful methods: `index()`, `create()`, `store()`).
- **User Preferences**: Button-driven UI with Bootstrap 5, maroon (#800000) and green (#28a745) color scheme.
- **Project Patterns**: Consistent use of `nasp_` prefix for view files, RESTful routes in `Routes.php`.
- **Challenges**: Handling parent-child relationships in `groups` table, AJAX for specific modules (e.g., Financial Claims).
- **Decisions**: Use of DomPDF for PDF generation, RBAC via `positions` table.
- **Tool Usage**: Integration with Augment Code and Cursor for code generation, Postman for API testing.

## Memory Bank Files for IPMS V1

Below are the detailed contents of each core Memory Bank file, customized for IPMS V1 and aligned with the provided system design, task list, UI notes, and prompt structure.

### projectbrief.md
```markdown
# Project Brief: Integrated Progress Monitoring System (IPMS) V1

## Overview
IPMS V1 is a web-based system built using **CodeIgniter 4** (MVC framework) and **MySQL**, designed to track human resources, organizational plans, budgets, and asset usage linked to **Workplans** and **Annual Activity Plans (AAP)**. It supports a RESTful API, role-based access control (RBAC), and a mobile-friendly, button-driven UI using Bootstrap 5.

## Core Requirements
- **Workplans**: Officers create workplans with recurring, project-based, or legislated activities, linked to budget codes and supervisors.
- **Budget Management**: Manage revenue/expenditure codes in budget books, linked to groups and activities.
- **Organizational Plans**: Support Corporate Plans (KRAs/KPIs) and Development Plans (Programs/Projects) with group assignments.
- **Financial Claims**: Create claims tied to workplan activities, generating FF3, FF4, and Alignment Sheets as PDFs using DomPDF.
- **User and Group Management**: Manage users, hierarchical group structures, and position assignments with CSV import support.
- **Reports and Acquittals**: Officers submit reports and acquittals for activities and claims.
- **Interface**: Button-driven navigation (no sidebar), with Bootstrap 5 for a mobile app-like experience.

## Goals
- Streamline progress tracking for government or organizational workflows.
- Ensure secure, role-based access (super admins, admins, editors, officers, supervisors, AROs, fund managers).
- Provide a responsive, intuitive UI with maroon (#800000) and green (#28a745) color scheme.
- Support RESTful APIs for data operations, tested via Postman.
- Generate accurate PDF forms (FF3, FF4, Alignment Sheets) with version tracking.
- Maintain a maintainable codebase using CodeIgniter 4 best practices.

## Scope
- Modules: User Management, Structure Management, Appointments, Plans Management, Budget Book Management, Workplan Management, Financial Claims, Reports and Acquittals, Legislated/Recurrent Activities, Dashboard.
- Database: MySQL with 20+ tables (e.g., `users`, `structures`, `workplans`, `claims`).
- UI: Bootstrap 5, DataTables.js for lists, Chart.js for visualizations, Font Awesome for icons.
- Security: RBAC via `positions` table, CSRF protection, password hashing.
- Development Tools: XAMPP, Composer, Augment Code, Cursor, Postman.

## Key Deliverables
- Fully functional CRUD operations for all modules.
- RESTful API endpoints for data access.
- Mobile-friendly UI with consistent button placement (top-right) and breadcrumbs.
- PDF generation for financial claims.
- Role-based dashboard with dynamic buttons.
- Comprehensive testing of UI and API endpoints.
```

### productContext.md
```markdown
# Product Context: IPMS V1

## Why IPMS Exists
IPMS V1 addresses the need for efficient tracking of organizational progress, budgets, and human resources in government or large institutions. It replaces manual processes with a digital system to:
- Monitor workplans and activities tied to organizational goals (KRAs/KPIs, Programs/Projects).
- Streamline financial claims and budget tracking with automated PDF form generation.
- Support hierarchical group structures and position assignments for accountability.
- Provide role-based access to ensure secure, relevant data access for users.

## Problems Solved
- **Fragmented Processes**: Consolidates workplan creation, budget allocation, and claim processing into one platform.
- **Manual Reporting**: Automates report and acquittal submissions, reducing paperwork.
- **Budget Oversight**: Links activities to budget codes, ensuring transparency and accountability.
- **Access Control**: Enforces RBAC to restrict sensitive actions (e.g., claim approvals to fund managers).
- **User Experience**: Delivers a mobile-friendly, button-driven interface for ease of use across devices.

## How It Works
- **Users**: Officers create workplans, submit claims, and file reports; admins manage users and structures; supervisors approve workplans; fund managers handle claim workflows.
- **Workplans**: Created annually, linked to budget codes, projects, or legislated/recurrent activities, with supervisor oversight.
- **Financial Claims**: Tied to workplan activities, generating FF3, FF4, and Alignment Sheets as PDFs.
- **Plans**: Corporate Plans (KRAs/KPIs) and Development Plans (Programs/Projects) align activities with organizational goals.
- **Interface**: Button-driven navigation (top-right buttons, no sidebar), with DataTables for lists and Chart.js for visualizations.

## User Experience Goals
- **Intuitive Navigation**: Button-based interface with large, touch-friendly buttons (Bootstrap 5 `btn-md`, `btn-lg`) and breadcrumbs for context.
- **Mobile-Friendly**: Responsive design using Bootstrap 5 grid, with stacked buttons and tables on mobile.
- **Visual Consistency**: Maroon (#800000) for primary actions, green (#28a745) for secondary actions, with Chart.js visualizations in matching tones.
- **Feedback**: Immediate validation feedback on forms, modals for confirmations, and alerts for errors.
- **Role-Based Access**: Tailored dashboard buttons (e.g., “Manage Users” for admins, “Submit Claim” for AROs) based on `positions` table fields.

## Key Users
- **Super Admins**: Full access to all modules, including user creation.
- **Admins**: Manage structures, plans, and limited user functions.
- **Editors**: Edit workplans and claims, view reports.
- **Officers**: Manage own workplans, claims, and reports.
- **Supervisors**: Approve workplans, monitor progress.
- **AROs/Fund Managers**: Handle financial claims and approvals.
```

### activeContext.md
```markdown
# Active Context: IPMS V1

## Current Work Focus
- Implementing CRUD operations for core modules (User Management, Structure Management, Workplans, Financial Claims, etc.) based on the provided task list.
- Developing a role-based dashboard with dynamic buttons for navigation.
- Setting up RESTful API endpoints for all modules, tested via Postman.
- Ensuring UI consistency with Bootstrap 5, DataTables.js, and Chart.js visualizations.
- Integrating DomPDF for PDF generation of FF3, FF4, and Alignment Sheets in the Financial Claims module.

## Recent Changes
- Established project setup with XAMPP, CodeIgniter 4, and MySQL (`ipms_db`).
- Defined database schema with 20+ tables (e.g., `users`, `workplans`, `claims`).
- Configured initial routes in `@app/Config/Routes.php` for dashboard and module access.
- Implemented Bootstrap 5-based UI with maroon/green color scheme, button-driven navigation, and no sidebar.
- Set up RBAC using `positions` table fields (`is_admin`, `is_fund_manager`, etc.).

## Next Steps
- Complete User Management module (Tasks 4.1–4.7): Implement `UserController`, views, and RESTful APIs.
- Develop Structure Management module (Tasks 5.1–5.6): Handle parent-child groups and positions.
- Implement Workplan Management module (Tasks 9.1–9.6): Link activities to budget codes and projects.
- Build Financial Claims module (Tasks 10.1–10.8): Include AJAX for activity selection and PDF generation.
- Test all modules for UI functionality and API responses (Tasks 14.1–14.6).
- Update `.clinerules` with patterns for CodeIgniter 4 controller naming and view prefixes.

## Active Decisions
- Use `nasp_` prefix for view files (e.g., `nasp_users_index.php`) to align with prompt conventions.
- Restrict AJAX to specific features (e.g., Financial Claims activity selection, Workplan activity creation).
- Store generated PDFs in `claim_forms` table with version tracking.
- Apply RBAC via `AuthFilter` checking `positions` table fields.
- Use standard CodeIgniter 4 form submissions for most CRUD operations.

## Considerations
- Ensure mobile responsiveness for all views, with stacked buttons on smaller screens.
- Validate parent-child relationships in `groups` table to prevent errors.
- Test CSV import functionality for appointments with sample data.
- Monitor performance of DataTables.js for large datasets (e.g., user or workplan lists).
```

### systemPatterns.md
```markdown
# System Patterns: IPMS V1

## System Architecture
- **Framework**: CodeIgniter 4, following MVC architecture for modular development.
- **API**: RESTful API for CRUD operations (GET, POST, PUT, DELETE) with JSON responses.
- **Database**: MySQL with 20+ tables (e.g., `users`, `structures`, `workplans`, `claims`).
- **UI**: Button-driven, mobile-friendly interface using Bootstrap 5, with no sidebar or top navigation.
- **PDF Generation**: DomPDF for generating FF3, FF4, and Alignment Sheets, stored in `claim_forms`.

## Key Design Patterns
- **MVC Pattern**:
  - **Controllers**: Handle HTTP requests, implement RESTful methods (e.g., `index()`, `store()`, `update($id)`).
  - **Models**: Manage database operations with validation rules (e.g., `UserModel` for `users` table).
  - **Views**: Rendered via `@app/Views/templates/main.php`, using Bootstrap 5 for styling.
- **RESTful API**:
  - Endpoints follow `/api/[resource]` (e.g., `/api/users`, `/api/workplans/{id}/activities`).
  - Secured with CSRF protection and API tokens (if implemented).
  - Returns HTTP status codes (200, 201, 400, etc.) with JSON payloads.
- **RBAC**:
  - Role-based access control using `positions` table fields (`is_admin`, `is_fund_manager`, `is_supervisor`).
  - Implemented via `AuthFilter` in CodeIgniter 4 to restrict routes.
- **Button-Driven Navigation**:
  - All navigation via top-right button groups (Bootstrap 5 `btn-primary` for maroon, `btn-secondary` for green).
  - Breadcrumbs on every page (except dashboard) for context.
  - “Back” button (green, `fa-arrow-left`) on all module pages.

## Component Relationships
- **Users ↔ Positions ↔ Appointments**: Users are assigned to positions via appointments, linked to groups and structures.
- **Workplans ↔ Activities ↔ Budget Codes**: Workplans contain activities linked to budget codes, projects, or legislated/recurrent activities.
- **Plans ↔ KRAs/KPIs, Programs/Projects**: Corporate Plans link to KRAs/KPIs, Development Plans to Programs/Projects, with group assignments.
- **Claims ↔ Activities ↔ Forms**: Claims are tied to workplan activities, generating PDFs stored in `claim_forms`.
- **Dashboard**: Central hub with role-based buttons, rendering module-specific views based on user permissions.

## Key Technical Decisions
- **File Naming**: Views use `nasp_` prefix (e.g., `nasp_users_index.php`) for consistency with prompts.
- **Form Handling**: Standard CodeIgniter 4 form submissions with CSRF protection; AJAX only for specific features (e.g., claim activity selection).
- **UI Libraries**: Bootstrap 5 for responsiveness, DataTables.js for lists, Chart.js for visualizations, Font Awesome for icons.
- **Security**: Password hashing in `UserModel`, CSRF protection for POST requests, RBAC via `positions` table.
- **PDF Storage**: Generated PDFs stored in `claim_forms` with version tracking for traceability.
```

### techContext.md
```markdown
# Technical Context: IPMS V1

## Technologies Used
- **Framework**: CodeIgniter 4 (PHP 7.4+), MVC architecture for backend development.
- **Database**: MySQL for data storage, with 20+ tables (e.g., `users`, `workplans`, `claims`).
- **Frontend**: Bootstrap 5 (via CDN) for responsive UI, DataTables.js for dynamic tables, Chart.js for visualizations, Font Awesome for icons.
- **PDF Generation**: DomPDF (via Composer: `composer require dompdf/dompdf`) for FF3, FF4, and Alignment Sheets.
- **JavaScript**: jQuery (via CDN) for AJAX in specific modules (e.g., Financial Claims activity selection).
- **Testing**: Postman for API testing, CodeIgniter 4’s testing library for unit tests.

## Development Setup
- **Server**: XAMPP (Apache, MySQL, PHP) for local development.
- **Installation**:
  - Install XAMPP with PHP 7.4+.
  - Create CodeIgniter 4 project: `composer create-project codeigniter4/framework ipms`.
  - Configure `.env` file: set base URL, database credentials, environment to `development`.
  - Install DomPDF: `composer require dompdf/dompdf`.
  - Include Bootstrap 5 and jQuery via CDN in `@app/Views/templates/main.php`.
- **Database**: Create `ipms_db` and execute provided SQL schema for tables.
- **Version Control**: Git repository with `.gitignore` for `writable/`, `.env`, etc.
- **IDE Tools**: Augment Code and Cursor for code generation, leveraging provided prompts.

## Technical Constraints
- **PHP Version**: Must be 7.4+ for CodeIgniter 4 compatibility.
- **No Sidebar**: UI relies on button-driven navigation, requiring consistent top-right button placement.
- **AJAX Usage**: Limited to specific features (e.g., Financial Claims activity selection, Workplan activity creation).
- **Database**: No new tables or models; use existing schema as defined.
- **Mobile-First**: UI must be responsive, with stacked buttons and tables on smaller screens.

## Dependencies
- **CodeIgniter 4**: Core framework for MVC and RESTful APIs.
- **MySQL**: Database for storing all data.
- **Bootstrap 5**: CSS framework for responsive UI.
- **DomPDF**: PDF generation library.
- **jQuery**: For AJAX functionality.
- **DataTables.js**: For dynamic, sortable tables.
- **Chart.js**: For visualizations (e.g., budget utilization, claim status).
- **Font Awesome**: For button and UI icons.

## Development Tools
- **XAMPP**: Local server environment.
- **Composer**: Dependency management for CodeIgniter 4 and DomPDF.
- **Postman**: Testing RESTful API endpoints.
- **Augment Code/Cursor**: AI-powered IDEs for code generation based on prompts.
- **Git**: Version control for codebase.
```

### progress.md
```markdown
# Progress: IPMS V1

## What Works
- **Project Setup**: XAMPP installed, CodeIgniter 4 project created, `.env` configured, Git repository initialized.
- **Database**: `ipms_db` created with all tables (e.g., `users`, `structures`, `workplans`) as per provided schema.
- **UI Foundation**: Bootstrap 5 integrated via CDN, main template (`@app/Views/templates/main.php`) set up with maroon/green color scheme.
- **Security**: CSRF protection enabled in `app/Config/Filters.php`, initial admin user seeded with hashed password.
- **Dashboard**: Basic structure implemented with role-based buttons planned but not fully functional.

## What’s Left to Build
- **User Management (Tasks 4.1–4.7)**: Complete `UserController`, views, and RESTful APIs; implement RBAC for admins.
- **Structure Management (Tasks 5.1–5.6)**: Develop `StructureController` for structures, groups, and positions; handle parent-child relationships.
- **Appointments (Tasks 6.1–6.6)**: Implement CRUD and CSV import functionality.
- **Plans Management (Tasks 7.1–7.6)**: Build CRUD for plans, KRAs, KPIs, programs, and projects with group assignments.
- **Budget Book Management (Tasks 8.1–8.6)**: Develop CRUD for budget books and codes.
- **Workplan Management (Tasks 9.1–9.6)**: Implement CRUD for workplans and activities, including AJAX for activity creation.
- **Financial Claims (Tasks 10.1–10.8)**: Complete CRUD, AJAX activity selection, and PDF generation with DomPDF.
- **Reports and Acquittals (Tasks 11.1–11.5)**: Implement CRUD for reports tied to activities and claims.
- **Legislated/Recurrent Activities (Tasks 12.1–12.5)**: Develop CRUD with group assignments.
- **Dashboard (Tasks 13.1–13.5)**: Finalize role-based buttons and Chart.js visualizations.
- **Testing (Tasks 14.1–14.6)**: Unit tests for models, API testing with Postman, UI testing across devices.
- **Deployment (Tasks 15.1–15.6)**: Optimize queries, minify assets, prepare production `.env`, document APIs.

## Current Status
- **Setup Phase**: Completed Tasks 1.1–1.8 (project setup) and 2.1–2.6 (database setup).
- **Authentication**: Partially implemented (Tasks 3.1–3.8); `LoginController` and `AuthFilter` in progress.
- **Modules**: Initial scaffolding for controllers and views started, but most CRUD operations and APIs are pending.
- **UI**: Basic template with Bootstrap 5 and button-driven navigation implemented; DataTables.js and Chart.js integration pending.
- **Testing**: Not yet started; planned for Postman (APIs) and browser (UI).

## Known Issues
- **RBAC**: `AuthFilter` needs testing to ensure role-based restrictions work correctly.
- **AJAX**: Potential CSRF issues with AJAX POST requests in Financial Claims and Workplan modules.
- **Performance**: Large DataTables lists (e.g., users, workplans) may need pagination (Task 16.1).
- **CSV Import**: Appointment CSV import logic needs validation for edge cases.
- **PDF Generation**: DomPDF integration untested; may face formatting issues with Alignment Sheets.
```

## .clinerules
```markdown
# .clinerules: IPMS V1 Project Intelligence

## Implementation Paths
- **Controller Structure**: Use RESTful methods in controllers (`index()`, `create()`, `store()`, `show($id)`, `edit($id)`, `update($id)`, `delete($id)`) for all modules.
- **View Naming**: Prefix view files with `nasp_` (e.g., `nasp_users_index.php`) to align with prompt conventions.
- **Routes**: Define RESTful routes in `@app/Config/Routes.php` (e.g., `GET /admin/users`, `POST /api/users`).
- **Form Handling**: Use standard CodeIgniter 4 form submissions with CSRF protection; AJAX only for Financial Claims (`store()`) and Workplan activities (`storeActivity()`).
- **PDF Generation**: Use DomPDF for FF3, FF4, and Alignment Sheets, storing files in `claim_forms` with version tracking.

## User Preferences
- **UI Design**: Button-driven navigation with no sidebar, using Bootstrap 5 `btn-primary` (maroon, #800000) for primary actions and `btn-secondary` (green, #28a745) for secondary actions (e.g., “Back”).
- **Colors**: Maroon for navbar, card headers, and primary buttons; green for back buttons and secondary actions.
- **Navigation**: Top-right button groups for all actions, with breadcrumbs on every page except dashboard.
- **Icons**: Use Font Awesome (e.g., `fa-plus` for “Create New”, `fa-arrow-left` for “Back”).
- **Tools**: Leverage Augment Code and Cursor for code generation, Postman for API testing.

## Project-Specific Patterns
- **RBAC**: Check `positions` table fields (`is_admin`, `is_fund_manager`, `is_supervisor`) via `AuthFilter` for route restrictions.
- **Database**: No new tables; use provided schema with indexes on frequently queried fields (e.g., `users.email`, `groups.structure_id`).
- **UI Consistency**: All views rendered via `@app/Views/templates/main.php`, with Bootstrap 5 grid, DataTables.js for lists, and Chart.js for visualizations.
- **API Responses**: Return JSON with HTTP status codes (200, 201, 400, etc.) for RESTful endpoints.

## Known Challenges
- **Parent-Child Groups**: Ensure valid `parent_group_id` in `groups` table to avoid circular references.
- **AJAX CSRF**: Debug CSRF issues for AJAX POST requests in Financial Claims and Workplan modules.
- **Large Datasets**: Implement pagination for DataTables lists (Task 16.1) to handle large user/workplan lists.
- **CSV Import**: Validate CSV format for appointments to prevent errors in `processImport()`.

## Evolution of Decisions
- **Initial Setup**: Adopted XAMPP and CodeIgniter 4 for rapid development, with Composer for dependency management.
- **UI Shift**: Moved from sidebar to button-driven navigation based on UI design notes, improving mobile usability.
- **RBAC Implementation**: Chose `positions` table for role checks instead of separate roles table for simplicity.
- **PDF Strategy**: Selected DomPDF over TCPDF for easier integration with CodeIgniter 4.

## Tool Usage Patterns
- **Augment Code/Cursor**: Use provided prompts to generate complete CRUD functions, ensuring consistency with `nasp_` view prefixes and RESTful routes.
- **Postman**: Test all API endpoints (e.g., `GET /api/users`, `POST /api/claims`) with valid and invalid inputs.
- **Git**: Commit changes after each module implementation, with clear messages (e.g., “Completed User Management CRUD”).
- **XAMPP**: Use for local development, with `development` environment in `.env` for debug toolbar.
```

## Notes
- **Task Coverage**: This Memory Bank covers the core development tasks (4.1–13.5) from the provided task list, focusing on MVC implementation, UI development, and API creation. Setup (1.1–1.8), authentication (3.1–3.8), testing (14.1–14.6), and deployment (15.1–15.6) tasks are partially reflected in `progress.md` and `techContext.md` but may require separate prompts for completion.
- **Prompt Integration**: The Memory Bank aligns with the provided prompt structure, ensuring compatibility with Augment Code and Cursor for generating complete CRUD functions (e.g., controllers, views, routes).
- **Future Updates**: As tasks are completed, update `progress.md` and `activeContext.md` to reflect new statuses, issues, and priorities. Add module-specific files (e.g., `memory-bank/user_management.md`) for detailed documentation if needed.
- **Consistency**: The Memory Bank ensures a cohesive approach to CodeIgniter 4 development, with emphasis on RESTful APIs, Bootstrap 5 UI, and RBAC, tailored to IPMS V1’s requirements.

This Memory Bank is designed to guide my work on IPMS V1, ensuring I can pick up tasks seamlessly after a memory reset. If you need specific code snippets, additional files, or an **update memory bank** to reflect new progress, let me know!