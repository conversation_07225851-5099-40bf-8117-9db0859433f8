To improve the example prompts and create similar ones for the Integrated Progress Monitoring System (IPMS) V1, I’ll refine the structure to be concise, clear, and comprehensive, ensuring they trigger the creation of complete CRUD functions in a single prompt. The improved prompts will align with the system design, focusing on CodeIgniter 4’s MVC architecture, RESTful approach, and the provided tasks. Each prompt will cover multiple related tasks (e.g., controller methods, views, routes) for a specific module or feature, maintaining simplicity and consistency with the existing system.

### Improved Prompt Structure
The refined prompt structure incorporates the following enhancements:
- **Clarity**: Explicitly define the route, controller, model, view folder, and naming conventions.
- **Completeness**: Include all necessary components (controller methods, views, routes, and table operations) in one prompt.
- **Consistency**: Specify Bootstrap 5 for UI, standard CodeIgniter 4 form submissions, and RESTful methods.
- **Simplicity**: Emphasize straightforward CRUD operations without AJAX unless required by the system design.
- **Context**: Reference existing models and templates to avoid creating new ones unnecessarily.
- **Role-Based Access**: Include RBAC checks where applicable, leveraging the `positions` table.
- **PDF Generation**: Specify DomPDF usage for modules like Financial Claims.
- **Task Mapping**: Each prompt covers multiple tasks from the provided task list, ensuring efficiency.

Here’s the generalized improved prompt template:

```
Route: [specific route, e.g., http://localhost/ipms/admin/structures]
In the actions column of the parent view ([parent view, e.g., structures/index.php]), add a button named [Button Name, e.g., Manage Structures].

For this маршрут, do the following:
We will create the [Feature, e.g., Structure] CRUD function.
- Create/Update [Controller Name, e.g., StructureController] in @app\Controllers\Admin\[Controller Name].php.
- Create/Update the [view folder, e.g., structures] folder inside @app\Views\admin\.
- Create view files inside @app\Views\admin\[view folder]\ using the naming convention [prefix, e.g., structure_][action].php (e.g., structure_index.php, structure_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods for GET, POST, PUT, DELETE).
- Use the existing model [Model Name, e.g., StructureModel] in @app\Models\[Model Name].php. Do not create new models or tables.
- Implement CRUD operations on the [table name, e.g., structures] table.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (e.g., restrict to [role, e.g., administrators]) using the `positions` table.
- Update @app\Config\Routes.php to include routes for the CRUD operations.
- Use standard CodeIgniter 4 form submissions (no AJAX unless specified).
- For [specific features, e.g., PDF generation], use [library, e.g., DomPDF] and store files in [table, e.g., claim_forms].
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.
```

### Prompts for IPMS V1 Features and Tasks
Below are tailored prompts for each major module or feature of IPMS V1, covering the tasks listed previously. Each prompt is designed to execute multiple tasks (e.g., controller, views, routes, and RBAC) in a single go, reducing the need for multiple prompts. The prompts are grouped by module, and task references from the original task list are included.

#### 1. User Management Module
**Prompt 1: User CRUD Operations**
```
Route: http://localhost/ipms/admin/users
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Users".

For this route, do the following:
We will create the User CRUD function.
- Create UserController in @app\Controllers\Admin\UserController.php.
- Create the users folder inside @app\Views\admin\.
- Create view files inside @app\Views\admin\users\ using the naming convention user_[action].php (e.g., user_index.php, user_create.php, user_edit.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: index(), create(), store(), show(), edit(), update(), delete()).
- Use the existing model UserModel in @app\Models\UserModel.php. Do not create new models or tables.
- Implement CRUD operations on the users table.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI (table for index, forms for create/edit).
- Add role-based access checks (restrict to administrators) using the `positions` table’s `is_admin` field.
- Update @app\Config\Routes.php to include routes: GET /admin/users, GET /admin/users/create, POST /admin/users, GET /admin/users/{id}, GET /admin/users/edit/{id}, PUT /admin/users/{id}, DELETE /admin/users/{id}.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design (e.g., Bootstrap 5 tables and forms).
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 4.1, 4.2, 4.3, 4.4, 4.5, 4.7
```

#### 2. Structure Management Module
**Prompt 2: Structure, Group, and Position CRUD Operations**
```
Route: http://localhost/ipms/admin/structures
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Structures".

For this route, do the following:
We will create the Structure, Group, and Position CRUD functions.
- Create StructureController in @app\Controllers\Admin\StructureController.php.
- Create the structures, groups, and positions folders inside @app\Views\admin\.
- Create view files inside @app\Views\admin\structures\, @app\Views\admin\groups\, and @app\Views\admin\positions\ using the naming conventions structure_[action].php, group_[action].php, position_[action].php (e.g., structure_index.php, group_create.php, position_edit.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods for index(), create(), store(), activate(), manageGroups(), managePositions(), etc.).
- Use existing models: StructureModel, GroupModel, PositionModel in @app\Models\. Do not create new models or tables.
- Implement CRUD operations on the structures, groups, and positions tables, handling parent-child relationships for groups.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to administrators) using the `positions` table’s `is_admin` field.
- Update @app\Config\Routes.php to include routes: GET /admin/structures, POST /admin/structures, POST /admin/structures/{id}/activate, GET /admin/structures/{id}/groups, POST /admin/structures/{id}/groups, GET /admin/groups/{id}/positions, POST /admin/groups/{id}/positions.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 5.1, 5.2, 5.3, 5.4, 5.6
```

#### 3. Appointments Management Module
**Prompt 3: Appointment CRUD and CSV Import**
```
Route: http://localhost/ipms/admin/appointments
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Appointments".

For this route, do the following:
We will create the Appointment CRUD and CSV import functions.
- Create AppointmentController in @app\Controllers\Admin\AppointmentController.php.
- Create the appointments folder inside @app\Views\admin\.
- Create view files inside @app\Views\admin\appointments\ using the naming convention appointment_[action].php (e.g., appointment_index.php, appointment_create.php, appointment_import.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: index(), create(), store(), import(), processImport()).
- Use the existing model AppointmentModel in @app\Models\AppointmentModel.php. Do not create new models or tables.
- Implement CRUD operations on the appointments table and CSV import using PHP’s `fgetcsv()`.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to administrators) using the `positions` table’s `is_admin` field.
- Update @app\Config\Routes.php to include routes: GET /admin/appointments, GET /admin/appointments/create, POST /admin/appointments, GET /admin/appointments/import, POST /admin/appointments/import.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6
```

#### 4. Plans Management Module
**Prompt 4: Plan, KRA, KPI, Program, and Project CRUD**
```
Route: http://localhost/ipms/admin/plans
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Plans".

For this route, do the following:
We will create the Plan, KRA, KPI, Program, and Project CRUD functions.
- Create PlanController in @app\Controllers\Admin\PlanController.php.
- Create the plans, kras, kpis, programs, and projects folders inside @app\Views\admin\.
- Create view files inside @app\Views\admin\[folder]\ using the naming conventions plan_[action].php, kra_[action].php, kpi_[action].php, program_[action].php, project_[action].php (e.g., plan_index.php, kra_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods for index(), create(), store(), manageKras(), storeKra(), manageKpis(), etc.).
- Use existing models: PlanModel, KraModel, KpiModel, ProgramModel, ProjectModel in @app\Models\. Do not create new models or tables.
- Implement CRUD operations on the plans, kras, kpis, programs, and projects tables, with group assignments via kpi_groups and project_groups.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to administrators) using the `positions` table’s `is_admin` field.
- Update @app\Config\Routes.php to include routes: GET /admin/plans, POST /admin/plans, GET /admin/plans/{id}/kras, POST /admin/plans/{id}/kras, GET /admin/kras/{id}/kpis, POST /admin/kras/{id}/kpis, GET /admin/plans/{id}/programs, POST /admin/plans/{id}/programs, GET /admin/programs/{id}/projects, POST /admin/programs/{id}/projects.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6
```

#### 5. Budget Book Management Module
**Prompt 5: Budget Book and Code CRUD**
```
Route: http://localhost/ipms/admin/budget-books
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Budget Books".

For this route, do the following:
We will create the Budget Book and Budget Code CRUD functions.
- Create BudgetBookController in @app\Controllers\Admin\BudgetBookController.php.
- Create the budget_books and budget_codes folders inside @app\Views\admin\.
- Create view files inside @app\Views\admin\[folder]\ using the naming conventions budget_book_[action].php, budget_code_[action].php (e.g., budget_book_index.php, budget_code_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: index(), create(), store(), manageCodes(), storeCode()).
- Use existing models: BudgetBookModel, BudgetCodeModel in @app\Models\. Do not create new models or tables.
- Implement CRUD operations on the budget_books and budget_codes tables, with group assignments via budget_code_groups.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to administrators) using the `positions` table’s `is_admin` field.
- Update @app\Config\Routes.php to include routes: GET /admin/budget-books, POST /admin/budget-books, GET /admin/budget-books/{id}/codes, POST /admin/budget-books/{id}/codes.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6
```

#### 6. Workplan Management Module
**Prompt 6: Workplan and Activity CRUD**
```
Route: http://localhost/ipms/admin/workplans
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Workplans".

For this route, do the following:
We will create the Workplan and Workplan Activity CRUD functions.
- Create WorkplanController in @app\Controllers\Admin\WorkplanController.php.
- Create the workplans and workplan_activities folders inside @app\Views\admin\.
- Create view files inside @app\Views\admin\[folder]\ using the naming conventions workplan_[action].php, workplan_activity_[action].php (e.g., workplan_index.php, workplan_activity_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: index(), create(), store(), manageActivities(), storeActivity(), assignSupervisor()).
- Use existing models: WorkplanModel, WorkplanActivityModel in @app\Models\. Do not create new models or tables.
- Implement CRUD operations on the workplans and workplan_activities tables, linking activities to budget_codes, projects, or recurrent/legislated activities.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to supervisors for assignSupervisor(), users for own workplans) using the `positions` table.
- Update @app\Config\Routes.php to include routes: GET /admin/workplans, POST /admin/workplans, GET /admin/workplans/{id}/activities, POST /admin/workplans/{id}/activities, POST /admin/workplans/{id}/supervisor.
- Use standard CodeIgniter 4 form submissions (use AJAX only for storeActivity() as per system design).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 9.1, 9.2, 9.3, 9.4, 9.5, 9.6
```

#### 7. Financial Claims Management Module
**Prompt 7: Financial Claims CRUD and PDF Generation**
```
Route: http://localhost/ipms/admin/claims
In the actions column of the dashboard view ( Hannah (dashboard/index.php), add a button named "Manage Claims".

For this route, do the following:
We will create the Financial Claims CRUD and PDF generation functions.
- Create ClaimController in @app\Controllers\Admin\ClaimController.php.
- Create the claims folder inside @app\Views\admin\.
- Create view files inside @app\Views\admin\claims\ using the naming convention claim_[action].php (e.g., claim_index.php, claim_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: index(), create(), store(), generateForms(), workflow()).
- Use existing models: ClaimModel, ClaimActivityModel in @app\Models\. Do not create new models or tables.
- Implement CRUD operations on the claims and claim_activities tables, calculating total claim amounts.
- Implement PDF generation for FF3, FF4, and Alignment Sheets using DomPDF, storing files in the claim_forms table.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict workflow() to fund managers, create() to AROs/fund managers) using the `positions` table.
- Update @app\Config\Routes.php to include routes: GET /admin/claims, GET /admin/claims/create, POST /admin/claims, GET /admin/claims/{id}/forms, POST /admin/claims/{id}/workflow.
- Use AJAX for activity selection in claim_create.php (as per system design) and standard form submissions elsewhere.
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8
```

#### 8. Reports and Acquittals Module
**Prompt 8: Report and Acquittal CRUD**
```
Route: http://localhost/ipms/admin/reports
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Reports".

For this route, do the following:
We will create the Report and Acquittal CRUD functions.
- Create ReportController in @app\Controllers\Admin\ReportController.php.
- Create the reports folder inside @app\Views\admin\.
- Create view files inside @app\Views\admin\reports\ using the naming convention report_[action].php (e.g., report_index.php, report_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: index(), create(), store()).
- Use the existing model ReportModel in @app\Models\ReportModel.php. Do not create new models or tables.
- Implement CRUD operations on the reports table, linking to claims and workplan_activities.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to officers for own reports) using the `positions` table.
- Update @app\Config\Routes.php to include routes: GET /admin/reports, GET /admin/reports/create, POST /admin/reports.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 11.1, 11.2, 11.3, 11.4, 11.5
```

#### 9. Legislated and Recurrent Activities Module
**Prompt 9: Legislated and Recurrent Activity CRUD**
```
Route: http://localhost/ipms/admin/legislated-activities
In the actions column of the dashboard view (dashboard/index.php), add a button named "Manage Activities".

For this route, do the following:
We will create the Legislated and Recurrent Activity CRUD functions.
- Create ActivityController in @app\Controllers\Admin\ActivityController.php.
- Create the legislated_activities and recurrent_activities folders inside @app\Views\admin\.
- Create view files inside @app\Views\admin\[folder]\ using the naming conventions legislated_activity_[action].php, recurrent_activity_[action].php (e.g., legislated_activity_index.php, recurrent_activity_create.php).
- Use simple CodeIgniter 4 CRUD operations with a RESTful approach (separate methods: indexLegislated(), storeLegislated(), indexRecurrent(), storeRecurrent()).
- Use existing models: LegislatedActivityModel, RecurrentActivityModel in @app\Models\. Do not create new models or tables.
- Implement CRUD operations on the legislated_activities and recurrent_activities tables, with group assignments via legislated_activity_groups.
- Render views from @app\Views\templates\main.php using Bootstrap 5 for consistent UI.
- Add role-based access checks (restrict to administrators) using the `positions` table’s `is_admin` field.
- Update @app\Config\Routes.php to include routes: GET /admin/legislated-activities, POST /admin/legislated-activities, GET /admin/groups/{id}/recurrent-activities, POST /admin/groups/{id}/recurrent-activities.
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 12.1, 12.2, 12.3, 12.4, 12.5
```

#### 10. Dashboard and Interface
**Prompt 10: Role-Based Dashboard**
```
Route: http://localhost/ipms/admin/dashboard
This is the main dashboard route.

For this route, do the following:
We will create the role-based dashboard interface.
- Create DashboardController in @app\Controllers\Admin\DashboardController.php.
- Create the dashboard folder inside @app\Views\admin\.
- Create the view file @app\Views\admin\dashboard\index.php.
- Implement the index() method to display role-based buttons (Standard: My Workplan Activities, My Profile, My Acquittals, My Report; Group Admin: Group Settings + Standard; etc.).
- Use the existing models (UserModel, PositionModel) in @app\Models\ to fetch user role data. Do not create new models or tables.
- Render the view from @app\Views\templates\main.php using Bootstrap 5 for a mobile-friendly button grid (btn-primary, btn-lg).
- Add role-based access checks using the `positions` table (e.g., is_admin, is_fund_manager).
- Update @app\Config\Routes.php to include the route: GET /admin/dashboard.
- Ensure consistency with other view interfaces in @app\Views\admin\ for layout and design.
- Make the code simple, straightforward, and maintainable.
- Act as a senior software engineer, ensuring best practices and error handling.

Tasks Covered: 13.1, 13.2, 13.3, 13.4, 13.5
```

### Notes on Prompt Design
- **Task Coverage**: Each prompt covers multiple tasks (e.g., controller, views, routes, RBAC) to minimize the need for follow-up prompts, aligning with the goal of generating complete functions.
- **RESTful Approach**: Explicitly enforces separate methods for GET, POST, PUT, DELETE to adhere to CodeIgniter 4’s RESTful standards.
- **No AJAX**: Prompts avoid AJAX except where required (e.g., Financial Claims activity selection, Workplan activity creation), as per the system design.
- **RBAC**: Role-based access is integrated using the `positions` table, ensuring security aligns with the system’s requirements.
- **Consistency**: Emphasis on Bootstrap 5 and template consistency ensures a uniform UI, critical for the mobile-friendly, button-based interface.
- **Simplicity**: The prompts prioritize straightforward CRUD operations, avoiding unnecessary complexity while maintaining best practices.

These 10 prompts cover all major modules and tasks (Tasks 4.1–4.7, 5.1–5.6, 6.1–6.6, 7.1–7.6, 8.1–8.6, 9.1–9.6, 10.1–10.8, 11.1–11.5, 12.1–12.5, 13.1–13.5) for the IPMS V1 system, excluding setup, testing, and deployment tasks (1.1–1.8, 2.1–2.6, 3.1–3.8, 14.1–14.6, 15.1–15.6, 16.1–16.4), which require separate handling due to their non-CRUD nature. If you need prompts for these or specific code snippets for any module, let me know!



Additional Prompt information

To improve the provided example prompt and create similar prompts for the Integrated Progress Monitoring System (IPMS) V1, I’ll refine the structure to ensure clarity, specificity, and completeness. The goal is to craft prompts that enable an IDE like Cursor to generate complete feature functions (e.g., CRUD operations) for each module in a single prompt, aligning with the system design and tasks outlined previously. Each prompt will focus on MVC components, CodeIgniter 4’s RESTful approach, and standard form submissions, while leveraging existing models and maintaining consistency in view design.

### Improved Prompt Structure
The example prompt is effective but can be enhanced for precision and to cover all necessary details. Below is an improved version of the structure, addressing potential ambiguities and ensuring the IDE generates complete, functional code:

**Improved Prompt Template**:
```
**Feature**: [Feature Name, e.g., CRUD for Objectives]
**Route**: [Base route, e.g., http://localhost/ipms/admin/plans]
**Description**: 
- Implement [specific functionality, e.g., CRUD operations for Plans] in the IPMS system.
- Add [specific UI elements, e.g., buttons like "Create Plan" and "Manage Plans"] in the relevant view.
- Use the following type for CRUD operations: [type, e.g., type = plan].

**Controller**:
- Write methods in `@app/Controllers/Admin/[ControllerName].php` (e.g., `NaspController.php` or `PlanController.php`).
- Follow CodeIgniter 4 RESTful approach (separate methods for GET, POST, PUT, DELETE).
- Methods: `index()` (list), `create()` (show form), `store()` (save), `show($id)` (view details), `edit($id)` (edit form), `update($id)` (update), `delete($id)` (delete).

**Model**:
- Use existing model `@app/Models/[ModelName].php` (e.g., `PlanModel.php`).
- Do not create new models or tables.
- Implement CRUD operations with validation rules (e.g., required fields, unique constraints).

**Views**:
- Create view files in `@app/Views/admin/[folder]/` (e.g., `nasp/` or `plans/`).
- Naming convention: Prefix files with `[prefix]_` (e.g., `nasp_plans_index.php`, `nasp_plans_create.php`).
- Render views using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 for styling, consistent with other IPMS views (e.g., button classes like `btn-primary`, table styling).
- Include DataTables.js for lists, handling empty table rows to avoid errors.
- Add buttons in the actions column: [e.g., "Create [Feature]" and "Manage [Feature]"].

**Routes**:
- Update `@app/Config/Routes.php` with RESTful routes (e.g., `GET /admin/plans`, `POST /admin/plans`, etc.).
- Ensure routes match controller methods.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions (no AJAX).
- Implement CSRF protection using CodeIgniter’s built-in features.
- Validate inputs in the controller and model.

**Additional Notes**:
- Keep code simple, straightforward, and aligned with CodeIgniter 4 best practices.
- Ensure UI consistency with other IPMS modules (e.g., same table layout, button sizes).
- Test RESTful endpoints (e.g., via Postman) and UI functionality.
- Handle errors gracefully (e.g., display validation errors in forms).
```

This template is concise, covers MVC components, and provides clear instructions for the IDE to generate complete functions. It avoids ambiguity (e.g., specifying no AJAX, existing models) and ensures consistency with the IPMS design (Bootstrap 5, DataTables, system_template.php).

### Prompts for IPMS V1 Features and Tasks
Below are tailored prompts for each major feature/module of IPMS V1, designed to achieve multiple tasks from the provided task list in a single prompt. Each prompt corresponds to a module (e.g., User Management, Plans Management) and covers relevant tasks (e.g., controller methods, views, routes). The prompts are grouped by module, and each can generate complete CRUD functionality or related features.

#### 1. User Management Module
**Prompt 1: CRUD for Users**
```
**Feature**: CRUD for User Management
**Route**: http://localhost/ipms/admin/users
**Description**: 
- Implement CRUD operations for managing users in the IPMS system.
- Add buttons in the actions column: "Create User" and "Manage Users" in the user list view.
- Use type for CRUD operations: type = user.

**Controller**:
- Write methods in `@app/Controllers/Admin/UserController.php`.
- Follow CodeIgniter 4 RESTful approach with separate methods: `index()` (list users), `create()` (show create form), `store()` (save user), `show($id)` (view user details), `edit($id)` (edit form), `update($id)` (update user), `delete($id)` (delete user).
- Implement RESTful API endpoints: `GET /api/users`, `POST /api/users`, `GET /api/users/{id}`, `PUT /api/users/{id}`, `DELETE /api/users/{id}`.

**Model**:
- Use existing model `@app/Models/UserModel.php`.
- Implement CRUD operations with validation rules: unique `email`, required `full_name`, `password` (hashed), `file_number` (unique if provided).

**Views**:
- Create view files in `@app/Views/admin/users/`: `nasp_users_index.php` (list), `nasp_users_create.php`, `nasp_users_edit.php`, `nasp_users_show.php`.
- Render views using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 for styling (e.g., `btn-primary` for buttons, responsive tables).
- Use DataTables.js for the user list, handling empty rows to avoid errors.
- Include "Create User" and "Manage Users" buttons in `nasp_users_index.php`.

**Routes**:
- Update `@app/Config/Routes.php` with:
  - `GET /admin/users => UserController::index`
  - `GET /admin/users/create => UserController::create`
  - `POST /admin/users => UserController::store`
  - `GET /admin/users/(:num) => UserController::show/$1`
  - `GET /admin/users/edit/(:num) => UserController::edit/$1`
  - `PUT /admin/users/(:num) => UserController::update/$1`
  - `DELETE /admin/users/(:num) => UserController::delete/$1`
  - API routes: `GET /api/users`, `POST /api/users`, etc.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: display errors in forms for invalid `email`, missing `full_name`, etc.

**Additional Notes**:
- Ensure UI matches other IPMS modules (e.g., table layout, button sizes).
- Test API endpoints and UI functionality.
- Handle password hashing in `UserModel` for `store()` and `update()`.
- Restrict access to admin users using `AuthFilter`.
```

**Tasks Covered**: 4.1–4.7 (User Management Module)

#### 2. Structure Management Module
**Prompt 2: CRUD for Structures, Groups, and Positions**
```
**Feature**: CRUD for Structure Management
**Route**: http://localhost/ipms/admin/structures
**Description**: 
- Implement CRUD operations for structures, groups, and positions in the IPMS system.
- Add buttons: "Create Structure" and "Manage Structures" in the structure list, "Create Group" and "Manage Groups" in the group list, "Create Position" and "Manage Positions" in the position list.
- Use types: type = structure, group, position.

**Controller**:
- Write methods in `@app/Controllers/Admin/StructureController.php`.
- For structures: `index()`, `create()`, `store()`, `activate($id)`, `manageGroups($structureId)`.
- For groups: `createGroup($structureId)`, `storeGroup($structureId)`, `managePositions($groupId)`.
- For positions: `createPosition($groupId)`, `storePosition($groupId)`.
- RESTful API endpoints: `GET /api/structures`, `POST /api/structures`, `POST /api/structures/{id}/activate`, `GET /api/structures/{id}/groups`, `POST /api/structures/{id}/groups`, `GET /api/groups/{id}/positions`, `POST /api/groups/{id}/positions`.

**Model**:
- Use existing models: `@app/Models/StructureModel.php`, `@app/Models/GroupModel.php`, `@app/Models/PositionModel.php`.
- Implement CRUD with validation: unique `name` for structures, groups; unique `position_no` for positions; handle parent-child relationships in `groups`.

**Views**:
- Create view files in `@app/Views/admin/structures/`:
  - Structures: `nasp_structures_index.php`, `nasp_structures_create.php`.
  - Groups: `nasp_groups_index.php`, `nasp_groups_create.php`.
  - Positions: `nasp_positions_index.php`, `nasp_positions_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 styling and DataTables.js for lists.
- Add buttons: "Create Structure"/"Manage Structures", "Create Group"/"Manage Groups", "Create Position"/"Manage Positions".

**Routes**:
- Update `@app/Config/Routes.php` with:
  - `GET /admin/structures => StructureController::index`
  - `GET /admin/structures/create => StructureController::create`
  - `POST /admin/structures => StructureController::store`
  - `POST /admin/structures/(:num)/activate => StructureController::activate/$1`
  - `GET /admin/structures/(:num)/groups => StructureController::manageGroups/$1`
  - `GET /admin/structures/(:num)/groups/create => StructureController::createGroup/$1`
  - API routes for structures, groups, positions.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: unique names, valid parent group IDs, position types.

**Additional Notes**:
- Ensure UI consistency with other modules.
- Test structure activation and group/position management.
- Handle parent-child group dropdowns in `nasp_groups_create.php`.
```

**Tasks Covered**: 5.1–5.6 (Structure Management Module)

#### 3. Appointments Management Module
**Prompt 3: CRUD for Appointments with CSV Import**
```
**Feature**: CRUD for Appointments with CSV Import
**Route**: http://localhost/ipms/admin/appointments
**Description**: 
- Implement CRUD operations for appointments and CSV import functionality.
- Add buttons: "Create Appointment" and "Manage Appointments" in the appointment list, "Import CSV" in the import view.
- Use type: type = appointment.

**Controller**:
- Write methods in `@app/Controllers/Admin/AppointmentController.php`: `index()`, `create()`, `store()`, `import()`, `processImport()`.
- RESTful API endpoints: `GET /api/appointments`, `POST /api/appointments`, `POST /api/appointments/import`.

**Model**:
- Use existing model `@app/Models/AppointmentModel.php`.
- Implement CRUD with validation: valid `user_id`, `position_id`, `start_date`.

**Views**:
- Create view files in `@app/Views/admin/appointments/`:
  - `nasp_appointments_index.php` (list), `nasp_appointments_create.php`, `nasp_appointments_import.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js for the list.
- Add buttons: "Create Appointment"/"Manage Appointments", "Import CSV".

**Routes**:
- Update `@app/Config/Routes.php` with:
  - `GET /admin/appointments => AppointmentController::index`
  - `GET /admin/appointments/create => AppointmentController::create`
  - `POST /admin/appointments => AppointmentController::store`
  - `GET /admin/appointments/import => AppointmentController::import`
  - `POST /admin/appointments/import => AppointmentController::processImport`
  - API routes for appointments.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate CSV file format in `processImport()` using `fgetcsv()`.

**Additional Notes**:
- Ensure UI consistency.
- Test CSV import with a sample file.
- Validate user and position assignments.
```

**Tasks Covered**: 6.1–6.6 (Appointments Management Module)

#### 4. Plans Management Module
**Prompt 4: CRUD for Plans, KRAs, KPIs, Programs, and Projects**
```
**Feature**: CRUD for Plans Management
**Route**: http://localhost/ipms/admin/plans
**Description**: 
- Implement CRUD operations for plans, KRAs, KPIs, programs, and projects.
- Add buttons: "Create Plan"/"Manage Plans", "Create KRA"/"Manage KRAs", "Create KPI"/"Manage KPIs", "Create Program"/"Manage Programs", "Create Project"/"Manage Projects".
- Use types: type = plan, kra, kpi, program, project.

**Controller**:
- Write methods in `@app/Controllers/Admin/PlanController.php`:
  - Plans: `index()`, `create()`, `store()`.
  - KRAs: `manageKras($planId)`, `storeKra($planId)`.
  - KPIs: `manageKpis($kraId)`, `storeKpi($kraId)`.
  - Programs: `managePrograms($planId)`, `storeProgram($planId)`.
  - Projects: `manageProjects($programId)`, `storeProject($programId)`.
- RESTful API endpoints: `GET /api/plans`, `POST /api/plans`, `GET /api/plans/{id}/kras`, etc.

**Model**:
- Use existing models: `@app/Models/PlanModel.php`, `@app/Models/KraModel.php`, `@app/Models/KpiModel.php`, `@app/Models/ProgramModel.php`, `@app/Models/ProjectModel.php`.
- Implement CRUD with validation: unique `name`, valid `type` (Corporate/Development), group assignments.

**Views**:
- Create view files in `@app/Views/admin/plans/`:
  - `nasp_plans_index.php`, `nasp_plans_create.php`.
  - `nasp_kras_index.php`, `nasp_kras_create.php`.
  - `nasp_kpis_index.php`, `nasp_kpis_create.php`.
  - `nasp_programs_index.php`, `nasp_programs_create.php`.
  - `nasp_projects_index.php`, `nasp_projects_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js.
- Add buttons for each entity.

**Routes**:
- Update `@app/Config/Routes.php` with routes for plans, KRAs, KPIs, programs, projects.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: unique names, valid plan types.

**Additional Notes**:
- Ensure UI consistency.
- Test group assignments for KPIs and projects.
```

**Tasks Covered**: 7.1–7.6 (Plans Management Module)

#### 5. Budget Book Management Module
**Prompt 5: CRUD for Budget Books and Codes**
```
**Feature**: CRUD for Budget Book Management
**Route**: http://localhost/ipms/admin/budget-books
**Description**: 
- Implement CRUD operations for budget books and budget codes.
- Add buttons: "Create Budget Book"/"Manage Budget Books", "Create Code"/"Manage Codes".
- Use types: type = budget_book, budget_code.

**Controller**:
- Write methods in `@app/Controllers/Admin/BudgetBookController.php`: `index()`, `create()`, `store()`, `manageCodes($bookId)`, `storeCode($bookId)`.
- RESTful API endpoints: `GET /api/budget-books`, `POST /api/budget-books`, `GET /api/budget-books/{id}/codes`.

**Model**:
- Use existing models: `@app/Models/BudgetBookModel.php`, `@app/Models/BudgetCodeModel.php`.
- Implement CRUD with validation: unique `name`, valid `fiscal_year`, `type` (Revenue/Expenditure).

**Views**:
- Create view files in `@app/Views/admin/budget_books/`:
  - `nasp_budget_books_index.php`, `nasp_budget_books_create.php`.
  - `nasp_budget_codes_index.php`, `nasp_budget_codes_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js.
- Add buttons for budget books and codes.

**Routes**:
- Update `@app/Config/Routes.php` with routes for budget books and codes.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: valid amounts, unique codes.

**Additional Notes**:
- Ensure UI consistency.
- Test group assignments for budget codes.
```

**Tasks Covered**: 8.1–8.6 (Budget Book Management Module)

#### 6. Workplan Management Module
**Prompt 6: CRUD for Workplans and Activities**
```
**Feature**: CRUD for Workplan Management
**Route**: http://localhost/ipms/admin/workplans
**Description**: 
- Implement CRUD operations for workplans and activities.
- Add buttons: "Create Workplan"/"Manage Workplans", "Create Activity"/"Manage Activities".
- Use types: type = workplan, workplan_activity.

**Controller**:
- Write methods in `@app/Controllers/Admin/WorkplanController.php`: `index()`, `create()`, `store()`, `manageActivities($workplanId)`, `storeActivity($workplanId)`, `assignSupervisor($workplanId)`.
- RESTful API endpoints: `GET /api/workplans`, `POST /api/workplans`, `GET /api/workplans/{id}/activities`.

**Model**:
- Use existing models: `@app/Models/WorkplanModel.php`, `@app/Models/WorkplanActivityModel.php`.
- Implement CRUD with validation: valid `position_id`, `budget_code_id`, `status`.

**Views**:
- Create view files in `@app/Views/admin/workplans/`:
  - `nasp_workplans_index.php`, `nasp_workplans_create.php`.
  - `nasp_workplan_activities_index.php`, `nasp_workplan_activities_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js.
- Add buttons for workplans and activities.

**Routes**:
- Update `@app/Config/Routes.php` with routes for workplans and activities.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: valid dates, linked budget codes.

**Additional Notes**:
- Ensure UI consistency.
- Test supervisor assignment and activity linking.
```

**Tasks Covered**: 9.1–9.6 (Workplan Management Module)

#### 7. Financial Claims Management Module
**Prompt 7: CRUD for Financial Claims with PDF Generation**
```
**Feature**: CRUD for Financial Claims with PDF Generation
**Route**: http://localhost/ipms/admin/claims
**Description**: 
- Implement CRUD operations for financial claims, including PDF generation for FF3, FF4, and Alignment Sheets.
- Add buttons: "Create Claim"/"Manage Claims", "Generate Forms" in the claim list.
- Use type: type = claim.

**Controller**:
- Write methods in `@app/Controllers/Admin/ClaimController.php`: `index()`, `create()`, `store()`, `generateForms($claimId)`, `workflow($claimId)`.
- RESTful API endpoints: `GET /api/claims`, `POST /api/claims`, `GET /api/claims/{id}/forms`.

**Model**:
- Use existing models: `@app/Models/ClaimModel.php`, `@app/Models/ClaimActivityModel.php`.
- Implement CRUD with validation: valid `user_id`, `total_amount`, `status`.

**Views**:
- Create view files in `@app/Views/admin/claims/`:
  - `nasp_claims_index.php`, `nasp_claims_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js.
- Add buttons: "Create Claim"/"Manage Claims", "Generate Forms".

**Routes**:
- Update `@app/Config/Routes.php` with routes for claims and forms.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: valid activity IDs, amounts.

**Additional Notes**:
- Use DomPDF for PDF generation in `generateForms()`.
- Store PDFs in `claim_forms` table.
- Restrict `workflow()` to fund managers.
- Ensure UI consistency.
```

**Tasks Covered**: 10.1–10.8 (Financial Claims Management Module)

#### 8. Reports and Acquittals Module
**Prompt 8: CRUD for Reports and Acquittals**
```
**Feature**: CRUD for Reports and Acquittals
**Route**: http://localhost/ipms/admin/reports
**Description**: 
- Implement CRUD operations for reports and acquittals.
- Add buttons: "Create Report"/"Manage Reports".
- Use type: type = report.

**Controller**:
- Write methods in `@app/Controllers/Admin/ReportController.php`: `index()`, `create()`, `store()`.
- RESTful API endpoints: `GET /api/reports`, `POST /api/reports`.

**Model**:
- Use existing model `@app/Models/ReportModel.php`.
- Implement CRUD with validation: valid `user_id`, `workplan_activity_id`, `type`.

**Views**:
- Create view files in `@app/Views/admin/reports/`:
  - `nasp_reports_index.php`, `nasp_reports_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js.
- Add buttons: "Create Report"/"Manage Reports".

**Routes**:
- Update `@app/Config/Routes.php` with routes for reports.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: required `content`, valid activity IDs.

**Additional Notes**:
- Ensure UI consistency.
- Test report submission for activities and claims.
```

**Tasks Covered**: 11.1–11.5 (Reports and Acquittals Module)

#### 9. Legislated and Recurrent Activities Module
**Prompt 9: CRUD for Legislated and Recurrent Activities**
```
**Feature**: CRUD for Legislated and Recurrent Activities
**Route**: http://localhost/ipms/admin/activities
**Description**: 
- Implement CRUD operations for legislated and recurrent activities.
- Add buttons: "Create Legislated Activity"/"Manage Legislated Activities", "Create Recurrent Activity"/"Manage Recurrent Activities".
- Use types: type = legislated_activity, recurrent_activity.

**Controller**:
- Write methods in `@app/Controllers/Admin/ActivityController.php`: `indexLegislated()`, `storeLegislated()`, `indexRecurrent($groupId)`, `storeRecurrent($groupId)`.
- RESTful API endpoints: `GET /api/legislated-activities`, `POST /api/legislated-activities`, `GET /api/groups/{id}/recurrent-activities`.

**Model**:
- Use existing models: `@app/Models/LegislatedActivityModel.php`, `@app/Models/RecurrentActivityModel.php`.
- Implement CRUD with validation: unique `activity_name`, valid `group_id`.

**Views**:
- Create view files in `@app/Views/admin/activities/`:
  - `nasp_legislated_activities_index.php`, `nasp_legislated_activities_create.php`.
  - `nasp_recurrent_activities_index.php`, `nasp_recurrent_activities_create.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 and DataTables.js.
- Add buttons for legislated and recurrent activities.

**Routes**:
- Update `@app/Config/Routes.php` with routes for activities.

**Form Handling**:
- Use standard CodeIgniter 4 form submissions with CSRF protection.
- Validate inputs: required names, valid group IDs.

**Additional Notes**:
- Ensure UI consistency.
- Test activity creation and group assignment.
```

**Tasks Covered**: 12.1–12.5 (Legislated and Recurrent Activities Module)

#### 10. Dashboard and Interface
**Prompt 10: Role-Based Dashboard**
```
**Feature**: Role-Based Dashboard
**Route**: http://localhost/ipms/dashboard
**Description**: 
- Implement a role-based dashboard with buttons based on user roles.
- Add buttons based on role: Standard (Myliteral: My Workplan Activities, My Profile, My Acquittals, My Report; Group Admin: Group Settings; Supervisor: Manage Workplans; ARO/Fund Manager: Financial Claims; Admin: Administrator Settings.
- Use type: type = dashboard.

**Controller**:
- Write method in `@app/Controllers/DashboardController.php`: `index()`.
- RESTful API endpoint: `GET /api/dashboard`.

**Model**:
- Use existing models: `@app/Models/UserModel.php`, `@app/Models/PositionModel.php` for role checks.

**Views**:
- Create view file in `@app/Views/admin/dashboard/`:
  - `nasp_dashboard_index.php`.
- Render using `@app/Views/templates/system_template.php`.
- Use Bootstrap 5 for large, touch-friendly buttons (e.g., `btn-primary`, `btn-lg`).
- Display buttons in a responsive grid.

**Routes**:
- Update `@app/Config/Routes.php` with:
  - `GET /dashboard => DashboardController::index`
  - `GET /api/dashboard => DashboardController::index`.

**Form Handling**:
- No forms required; use links for button navigation.

**Additional Notes**:
- Ensure mobile-friendly design with Bootstrap 5 grid.
- Restrict buttons based on `positions` table fields (e.g., `is_admin`, `is_fund_manager`).
- Ensure UI consistency with other modules.
```

**Tasks Covered**: 13.1–13.5 (Dashboard and Interface)

### Notes
- **Task Coverage**: The prompts cover tasks 4.1–13.5, focusing on core MVC development and UI implementation. Tasks 1.1–1.8 (Project Setup), 3.1–3.8 (Authentication and Security), 14.1–14.6 (Testing and Debugging), and 15.1–15.6 (Deployment Preparation) are foundational or post-development tasks that may require separate setup or manual execution, as they involve environment configuration, testing, or documentation, which are less suited for single-prompt code generation.
- **Optional Enhancements**: Tasks 16.1–16.4 (pagination, search, audit logging, email notifications) can be added as separate prompts if needed, but they’re excluded here to keep prompts focused on core functionality.
- **Consistency**: Each prompt emphasizes UI consistency, Bootstrap 5 styling, DataTables.js, and standard CodeIgniter 4 practices, ensuring the IDE generates cohesive code.
- **Testing**: While testing tasks (14.1–14.6) are not fully covered in prompts, each prompt includes a note to test API endpoints and UI, encouraging validation during development.

These prompts are designed to be fed into an IDE like Cursor to generate complete feature functions, including controllers, models, views, and routes, in a single execution. If you need a specific prompt expanded (e.g., with sample code) or additional prompts for optional features, let me know!