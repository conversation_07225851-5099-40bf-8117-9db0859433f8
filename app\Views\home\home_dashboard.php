<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - IPMS</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= base_url('public/assets/themes/home_themes/css/style.css') ?>" rel="stylesheet">

    <style>
        body {
            background-color: #f8f9fa;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-card.maroon {
            background: linear-gradient(135deg, #800000, #a00000);
        }

        .stat-card.blue {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .stat-card.orange {
            background: linear-gradient(135deg, #fd7e14, #e55a00);
        }

        .module-btn {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #343a40;
            display: block;
            height: 100%;
        }

        .module-btn:hover {
            border-color: #28a745;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            color: #28a745;
            text-decoration: none;
        }

        .module-btn i {
            font-size: 3rem;
            color: #800000;
            margin-bottom: 1rem;
            display: block;
        }

        .module-btn:hover i {
            color: #28a745;
        }

        .welcome-section {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.3s ease;
        }

        .activity-item:hover {
            background-color: #f8f9fa;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .activity-icon.success {
            background-color: #28a745;
        }

        .activity-icon.warning {
            background-color: #ffc107;
        }

        .activity-icon.info {
            background-color: #17a2b8;
        }

        .activity-icon.danger {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #28a745;">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <img src="<?= base_url('public/assets/system_images/ipms-icon.png') ?>" alt="IPMS" height="30" class="me-2">
                IPMS Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>Admin User
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= site_url('home') ?>"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="fw-bold mb-2">Welcome back, Admin!</h2>
                    <p class="mb-0 opacity-75">Here's what's happening with your projects today.</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <span id="currentDate"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <i class="fas fa-clipboard-list fs-1 mb-3"></i>
                    <h3 class="fw-bold">24</h3>
                    <p class="mb-0">Active Workplans</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card maroon">
                    <i class="fas fa-dollar-sign fs-1 mb-3"></i>
                    <h3 class="fw-bold">12</h3>
                    <p class="mb-0">Pending Claims</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card blue">
                    <i class="fas fa-users fs-1 mb-3"></i>
                    <h3 class="fw-bold">156</h3>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card orange">
                    <i class="fas fa-chart-bar fs-1 mb-3"></i>
                    <h3 class="fw-bold">89%</h3>
                    <p class="mb-0">Budget Utilization</p>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="row g-4">
            <!-- System Modules -->
            <div class="col-lg-8">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #28a745;">
                        <i class="fas fa-th-large me-2"></i>System Modules
                    </h4>
                    <div class="row g-3">
                        <div class="col-lg-4 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-users"></i>
                                <h6 class="fw-bold">User Management</h6>
                                <small class="text-muted">Manage users & roles</small>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-sitemap"></i>
                                <h6 class="fw-bold">Structure Management</h6>
                                <small class="text-muted">Organizational structure</small>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-clipboard-list"></i>
                                <h6 class="fw-bold">Workplans</h6>
                                <small class="text-muted">Create & manage plans</small>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-dollar-sign"></i>
                                <h6 class="fw-bold">Financial Claims</h6>
                                <small class="text-muted">Process claims</small>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-chart-bar"></i>
                                <h6 class="fw-bold">Reports</h6>
                                <small class="text-muted">Generate reports</small>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-book"></i>
                                <h6 class="fw-bold">Budget Books</h6>
                                <small class="text-muted">Manage budgets</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="col-lg-4">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #28a745;">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h4>
                    <div class="recent-activity">
                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon success me-3">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Workplan Approved</h6>
                                <p class="mb-1 text-muted small">Q1 Marketing Plan has been approved by supervisor</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon info me-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">New User Added</h6>
                                <p class="mb-1 text-muted small">John Doe has been added to the system</p>
                                <small class="text-muted">4 hours ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon warning me-3">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Budget Alert</h6>
                                <p class="mb-1 text-muted small">IT Department budget is 85% utilized</p>
                                <small class="text-muted">6 hours ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon success me-3">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Report Generated</h6>
                                <p class="mb-1 text-muted small">Monthly progress report has been generated</p>
                                <small class="text-muted">1 day ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon danger me-3">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Claim Rejected</h6>
                                <p class="mb-1 text-muted small">Travel claim #TC-001 requires additional documentation</p>
                                <small class="text-muted">2 days ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row g-4 mt-2">
            <div class="col-12">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #28a745;">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h4>
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-plus-circle me-2"></i>Create Workplan
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-file-invoice-dollar me-2"></i>Submit Claim
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-chart-line me-2"></i>View Reports
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-user-plus me-2"></i>Add User
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set current date
            const currentDate = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            document.getElementById('currentDate').textContent = currentDate.toLocaleDateString('en-US', options);

            // Add click handlers for module buttons
            const moduleButtons = document.querySelectorAll('.module-btn');
            moduleButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const moduleName = this.querySelector('h6').textContent;
                    alert(`${moduleName} module is not yet implemented in this demo.`);
                });
            });

            // Add click handlers for quick action buttons
            const quickActionButtons = document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-info, .btn-outline-warning');
            quickActionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const actionName = this.textContent.trim();
                    alert(`${actionName} functionality is not yet implemented in this demo.`);
                });
            });

            // Animate statistics on page load
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });

            // Add hover effects to dashboard cards
            const dashboardCards = document.querySelectorAll('.dashboard-card');
            dashboardCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            console.log('IPMS Dashboard initialized successfully');
        });
    </script>
</body>
</html>