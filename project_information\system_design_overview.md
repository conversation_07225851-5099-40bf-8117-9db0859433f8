Below is a detailed system design for the **Integrated Progress Monitoring System (IPMS) V1**, built using **CodeIgniter 4** and **MySQL**, following a **RESTful** approach for functions and methods. The design includes a detailed list of system functions (focusing on MVC and CRUD operations), proposed MySQL database tables with fields, and considerations for the system architecture based on the provided requirements.

---

### System Overview
**IPMS** is a progress monitoring system that tracks human resources, organizational plans, budgets, and asset usage, all linked to **Workplans** and **Annual Activity Plans (AAP)**. It supports:
- **Workplans**: Created by officers, containing recurring activities, projects, and legislated activities.
- **Budget Management**: Links activities to budget codes (revenue/expenditure).
- **Organizational Plans**: Corporate Plans (KRAs/KPIs) and Development Plans (Programs/Projects).
- **Financial Claims**: Tied to workplan activities, generating FF3, FF4 forms, and Alignment Sheets in PDF.
- **User and Group Management**: Hierarchical group structures with position assignments.
- **Reporting and Acquittals**: Officers submit reports and acquittals for financial claims.

The system uses **CodeIgniter 4** for the backend, **MySQL** for the database, **Bootstrap 5** for the frontend, and a button-based interface to mimic a mobile app experience.

---

### System Architecture
1. **Frontend**:
   - **Framework**: Bootstrap 5 for responsive, mobile-friendly UI.
   - **Interface**: Button-based navigation within the main content area, no sidebar or top navigation.
   - **Forms**: Standard CodeIgniter forms for most CRUD operations, with AJAX for specific submissions (e.g., financial claims, form generation).

2. **Backend**:
   - **Framework**: CodeIgniter 4, following MVC architecture.
   - **API**: RESTful API for data operations (GET, POST, PUT, DELETE).
   - **Database**: MySQL for data storage.
   - **PDF Generation**: Library like TCPDF or DomPDF for FF3, FF4, and Alignment Sheet generation.

3. **Development Environment**:
   - **Server**: XAMPP (Apache, MySQL, PHP).
   - **PHP Version**: Compatible with CodeIgniter 4 (PHP 7.4+).

4. **Security**:
   - Role-based access control (RBAC) for administrators, officers, supervisors, fund managers, AROs, and group admins.
   - Input validation and CSRF protection using CodeIgniter’s built-in features.
   - Password hashing for user authentication.

---

### System Functions List
The system functions are organized by **MVC** (Model-View-Controller) components and focus on **CRUD** operations for each module. RESTful endpoints are defined for key operations.

#### 1. User Management
**Purpose**: Manage users (officers, administrators, etc.) with personal details and authentication.
- **Controller**: `UserController`
  - `index()`: List all users (GET `/api/users`).
  - `create()`: Display create user form (GET `/users/create`).
  - `store()`: Save new user (POST `/api/users`).
  - `show($id)`: Display user details (GET `/api/users/{id}`).
  - `edit($id)`: Display edit user form (GET `/users/edit/{id}`).
  - `update($id)`: Update user details (PUT `/api/users/{id}`).
  - `delete($id)`: Delete user (DELETE `/api/users/{id}`).
- **Model**: `UserModel`
  - CRUD operations for `users` table.
  - Validation rules (e.g., unique email, required fields).
- **View**:
  - `users/index.php`: List users with edit/delete buttons.
  - `users/create.php`: Form for adding a user.
  - `users/edit.php`: Form for editing a user.
- **RESTful Endpoints**:
  - GET `/api/users`: Retrieve all users.
  - POST `/api/users`: Create a new user.
  - GET `/api/users/{id}`: Get user by ID.
  - PUT `/api/users/{id}`: Update user.
  - DELETE `/api/users/{id}`: Delete user.

#### 2. Structure Management
**Purpose**: Manage organizational structures, groups, and positions.
- **Controller**: `StructureController`
  - `index()`: List all structures (GET `/api/structures`).
  - `create()`: Create structure form (GET `/structures/create`).
  - `store()`: Save structure (POST `/api/structures`).
  - `activate($id)`: Activate a structure (POST `/api/structures/{id}/activate`).
  - `manageGroups($structureId)`: List groups in a structure (GET `/api/structures/{id}/groups`).
  - `createGroup($structureId)`: Create group form (GET `/structures/{id}/groups/create`).
  - `storeGroup($structureId)`: Save group (POST `/api/structures/{id}/groups`).
  - `managePositions($groupId)`: List positions in a group (GET `/api/groups/{id}/positions`).
  - `createPosition($groupId)`: Create position form (GET `/groups/{id}/positions/create`).
  - `storePosition($groupId)`: Save position (POST `/api/groups/{id}/positions`).
- **Model**: `StructureModel`, `GroupModel`, `PositionModel`
  - CRUD for `structures`, `groups`, `positions` tables.
  - Handle parent-child relationships for groups.
- **View**:
  - `structures/index.php`: List structures with activate button.
  - `groups/index.php`: List groups with add/edit/delete buttons.
  - `positions/index.php`: List positions with add/edit/delete buttons.
- **RESTful Endpoints**:
  - GET `/api/structures`: List structures.
  - POST `/api/structures`: Create structure.
  - POST `/api/structures/{id}/activate`: Activate structure.
  - GET `/api/structures/{id}/groups`: List groups in structure.
  - POST `/api/structures/{id}/groups`: Create group.
  - GET `/api/groups/{id}/positions`: List positions in group.
  - POST `/api/groups/{id}/positions`: Create position.

#### 3. Appointments Management
**Purpose**: Assign users to positions, with CSV import option.
- **Controller**: `AppointmentController`
  - `index()`: List appointments (GET `/api/appointments`).
  - `create()`: Create appointment form (GET `/appointments/create`).
  - `store()`: Save appointment (POST `/api/appointments`).
  - `import()`: Display CSV import form (GET `/appointments/import`).
  - `processImport()`: Process CSV file (POST `/api/appointments/import`).
- **Model**: `AppointmentModel`
  - CRUD for `appointments` table.
  - Validate position and user assignments.
- **View**:
  - `appointments/index.php`: List appointments.
  - `appointments/create.php`: Form to assign user to position.
  - `appointments/import.php`: CSV upload form.
- **RESTful Endpoints**:
  - GET `/api/appointments`: List appointments.
  - POST `/api/appointments`: Create appointment.
  - POST `/api/appointments/import`: Import appointments from CSV.

#### 4. Plans Management
**Purpose**: Manage Corporate Plans (KRAs/KPIs) and Development Plans (Programs/Projects).
- **Controller**: `PlanController`
  - `index()`: List plans (GET `/api/plans`).
  - `create()`: Create plan form (GET `/plans/create`).
  - `store()`: Save plan (POST `/api/plans`).
  - `manageKras($planId)`: List KRAs for corporate plan (GET `/api/plans/{id}/kras`).
  - `storeKra($planId)`: Save KRA (POST `/api/plans/{id}/kras`).
  - `manageKpis($kraId)`: List KPIs (GET `/api/kras/{id}/kpis`).
  - `storeKpi($kraId)`: Save KPI (POST `/api/kras/{id}/kpis`).
  - `managePrograms($planId)`: List programs for development plan (GET `/api/plans/{id}/programs`).
  - `storeProgram($planId)`: Save program (POST `/api/plans/{id}/programs`).
  - `manageProjects($programId)`: List projects (GET `/api/programs/{id}/projects`).
  - `storeProject($programId)`: Save project (POST `/api/programs/{id}/projects`).
- **Model**: `PlanModel`, `KraModel`, `KpiModel`, `ProgramModel`, `ProjectModel`
  - CRUD for `plans`, `kras`, `kpis`, `programs`, `projects` tables.
  - Link KPIs and projects to groups.
- **View**:
  - `plans/index.php`: List plans by type.
  - `kras/index.php`: List KRAs with add KPI button.
  - `programs/index.php`: List programs with add project button.
- **RESTful Endpoints**:
  - GET `/api/plans`: List plans.
  - POST `/api/plans`: Create plan.
  - GET `/api/plans/{id}/kras`: List KRAs.
  - POST `/api/plans/{id}/kras`: Create KRA.
  - GET `/api/kras/{id}/kpis`: List KPIs.
  - POST `/api/kras/{id}/kpis`: Create KPI.
  - GET `/api/plans/{id}/programs`: List programs.
  - POST `/api/plans/{id}/programs`: Create program.
  - GET `/api/programs/{id}/projects`: List projects.
  - POST `/api/programs/{id}/projects`: Create project.

#### 5. Budget Book Management
**Purpose**: Manage budget books with revenue and expenditure codes.
- **Controller**: `BudgetBookController`
  - `index()`: List budget books (GET `/api/budget-books`).
  - `create()`: Create budget book form (GET `/budget-books/create`).
  - `store()`: Save budget book (POST `/api/budget-books`).
  - `manageCodes($bookId)`: List codes (GET `/api/budget-books/{id}/codes`).
  - `storeCode($bookId)`: Save code (POST `/api/budget-books/{id}/codes`).
- **Model**: `BudgetBookModel`, `BudgetCodeModel`
  - CRUD for `budget_books`, `budget_codes` tables.
  - Link codes to groups.
- **View**:
  - `budget_books/index.php`: List budget books.
  - `budget_codes/index.php`: List codes with add/edit/delete buttons.
- **RESTful Endpoints**:
  - GET `/api/budget-books`: List budget books.
  - POST `/api/budget-books`: Create budget book.
  - GET `/api/budget-books/{id}/codes`: List codes.
  - POST `/api/budget-books/{id}/codes`: Create code.

#### 6. Workplan Management
**Purpose**: Create and manage workplans with activities.
- **Controller**: `WorkplanController`
  - `index()`: List user’s workplans (GET `/api/workplans`).
  - `create()`: Create workplan form (GET `/workplans/create`).
  - `store()`: Save workplan (POST `/api/workplans`).
  - `manageActivities($workplanId)`: List activities (GET `/api/workplans/{id}/activities`).
  - `storeActivity($workplanId)`: Save activity (POST `/api/workplans/{id}/activities`, AJAX).
  - `assignSupervisor($workplanId)`: Assign supervisor (POST `/api/workplans/{id}/supervisor`).
- **Model**: `WorkplanModel`, `WorkplanActivityModel`
  - CRUD for `workplans`, `workplan_activities` tables.
  - Link activities to budget codes, projects, or recurrent/legislated activities.
- **View**:
  - `workplans/index.php`: List workplans with add activity button.
  - `workplan_activities/index.php`: List activities with edit/delete buttons.
- **RESTful Endpoints**:
  - GET `/api/workplans`: List workplans.
  - POST `/api/workplans`: Create workplan.
  - GET `/api/workplans/{id}/activities`: List activities.
  - POST `/api/workplans/{id}/activities`: Create activity.
  - POST `/api/workplans/{id}/supervisor`: Assign supervisor.

#### 7. Financial Claims Management
**Purpose**: Manage financial claims tied to workplan activities, generate FF3/FF4 forms and Alignment Sheets.
- **Controller**: `ClaimController`
  - `index()`: List claims (GET `/api/claims`).
  - `create()`: Create claim form (GET `/claims/create`, AJAX for activity selection).
  - `store()`: Save claim (POST `/api/claims`, AJAX).
  - `generateForms($claimId)`: Generate FF3/FF4 and Alignment Sheet (GET `/api/claims/{id}/forms`).
  - `workflow($claimId)`: Approve/reject claim (POST `/api/claims/{id}/workflow`).
- **Model**: `ClaimModel`, `ClaimActivityModel`
  - CRUD for `claims`, `claim_activities` tables.
  - Calculate total claim amount and track budget usage.
- **View**:
  - `claims/index.php`: List claims with generate forms button.
  - `claims/create.php`: Form to select activities and enter amounts (AJAX).
- **RESTful Endpoints**:
  - GET `/api/claims`: List claims.
  - POST `/api/claims`: Create claim.
  - GET `/api/claims/{id}/forms`: Generate FF3/FF4 and Alignment Sheet.
  - POST `/api/claims/{id}/workflow`: Update claim status.

#### 8. Reports and Acquittals
**Purpose**: Officers submit reports and acquittals for workplan activities and claims.
- **Controller**: `ReportController`
  - `index()`: List reports/acquittals (GET `/api/reports`).
  - `create()`: Create report form (GET `/reports/create`).
  - `store()`: Save report/acquittal (POST `/api/reports`).
- **Model**: `ReportModel`
  - CRUD for `reports` table.
  - Link to claims and activities.
- **View**:
  - `reports/index.php`: List reports/acquittals.
  - `reports/create.php`: Form for report/acquittal submission.
- **RESTful Endpoints**:
  - GET `/api/reports`: List reports.
  - POST `/api/reports`: Create report/acquittal.

#### 9. Legislated and Recurrent Activities
**Purpose**: Manage pre-listed legislated and group-specific recurrent activities.
- **Controller**: `ActivityController`
  - `indexLegislated()`: List legislated activities (GET `/api/legislated-activities`).
  - `storeLegislated()`: Save legislated activity (POST `/api/legislated-activities`).
  - `indexRecurrent($groupId)`: List recurrent activities for a group (GET `/api/groups/{id}/recurrent-activities`).
  - `storeRecurrent($groupId)`: Save recurrent activity (POST `/api/groups/{id}/recurrent-activities`).
- **Model**: `LegislatedActivityModel`, `RecurrentActivityModel`
  - CRUD for `legislated_activities`, `recurrent_activities` tables.
- **View**:
  - `legislated_activities/index.php`: List legislated activities.
  - `recurrent_activities/index.php`: List recurrent activities for a group.
- **RESTful Endpoints**:
  - GET `/api/legislated-activities`: List legislated activities.
  - POST `/api/legislated-activities`: Create legislated activity.
  - GET `/api/groups/{id}/recurrent-activities`: List recurrent activities.
  - POST `/api/groups/{id}/recurrent-activities`: Create recurrent activity.

#### 10. Interface and Menu
**Purpose**: Display role-based buttons in the main content area.
- **Controller**: `DashboardController`
  - `index()`: Display role-based buttons (GET `/dashboard`).
- **View**: `dashboard/index.php`
  - Buttons based on user role:
    - **Standard**: My Workplan Activities, My Profile, My Acquittals, My Report.
    - **Group Admin**: Group Settings + Standard.
    - **Supervisor**: Manage Workplans + Standard.
    - **ARO**: Financial Claims + Standard.
    - **Fund Manager**: Financial Claims, Claims Workflow + Standard.
    - **Administrator**: Administrator Settings + Standard.
- **RESTful Endpoint**:
  - GET `/api/dashboard`: Retrieve user-specific menu buttons.

---

### MySQL Database Tables
Below are the proposed MySQL tables with fields, based on the system requirements.

```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    file_number VARCHAR(50) UNIQUE,
    gender ENUM('Male', 'Female', 'Other'),
    date_of_birth DATE,
    date_joined DATE,
    id_photo VARCHAR(255),
    contact_details TEXT,
    remarks TEXT,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE structures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    structure_id INT NOT NULL,
    parent_group_id INT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (structure_id) REFERENCES structures(id),
    FOREIGN KEY (parent_group_id) REFERENCES groups(id)
);

CREATE TABLE positions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    position_no VARCHAR(50) UNIQUE,
    position_name VARCHAR(255) NOT NULL,
    grade VARCHAR(50),
    reporting_to_id INT NULL,
    position_type ENUM('Public Servant', 'Casual') NOT NULL,
    is_fund_manager BOOLEAN DEFAULT FALSE,
    is_supervisor BOOLEAN DEFAULT FALSE,
    is_group_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES groups(id),
    FOREIGN KEY (reporting_to_id) REFERENCES positions(id)
);

CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    position_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (position_id) REFERENCES positions(id)
);

CREATE TABLE plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type ENUM('Corporate', 'Development') NOT NULL,
    fiscal_year YEAR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE kras (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES plans(id)
);

CREATE TABLE kpis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kra_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (kra_id) REFERENCES kras(id)
);

CREATE TABLE kpi_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kpi_id INT NOT NULL,
    group_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (kpi_id) REFERENCES kpis(id),
    FOREIGN KEY (group_id) REFERENCES groups(id)
);

CREATE TABLE programs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES plans(id)
);

CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    program_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (program_id) REFERENCES programs(id)
);

CREATE TABLE project_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    group_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (group_id) REFERENCES groups(id)
);

CREATE TABLE project_milestones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    due_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

CREATE TABLE budget_books (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fiscal_year YEAR NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE budget_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    budget_book_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    item VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type ENUM('Revenue', 'Expenditure') NOT NULL,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_book_id) REFERENCES budget_books(id)
);

CREATE TABLE budget_code_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    budget_code_id INT NOT NULL,
    group_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_code_id) REFERENCES budget_codes(id),
    FOREIGN KEY (group_id) REFERENCES groups(id)
);

CREATE TABLE legislated_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    legislation_name VARCHAR(255) NOT NULL,
    activity_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE legislated_activity_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    legislated_activity_id INT NOT NULL,
    group_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (legislated_activity_id) REFERENCES legislated_activities(id),
    FOREIGN KEY (group_id) REFERENCES groups(id)
);

CREATE TABLE recurrent_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    activity_group VARCHAR(255) NOT NULL,
    activity_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES groups(id)
);

CREATE TABLE workplans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    position_id INT NOT NULL,
    supervisor_position_id INT NOT NULL,
    fiscal_year YEAR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (position_id) REFERENCES positions(id),
    FOREIGN KEY (supervisor_position_id) REFERENCES positions(id)
);

CREATE TABLE workplan_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    workplan_id INT NOT NULL,
    position_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    budget_code_id INT NULL,
    project_milestone_id INT NULL,
    recurrent_activity_id INT NULL,
    legislated_activity_id INT NULL,
    asset_id INT NULL,
    start_date DATE,
    end_date DATE,
    status ENUM('Pending', 'In Progress', 'Completed') DEFAULT 'Pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workplan_id) REFERENCES workplans(id),
    FOREIGN KEY (position_id) REFERENCES positions(id),
    FOREIGN KEY (budget_code_id) REFERENCES budget_codes(id),
    FOREIGN KEY (project_milestone_id) REFERENCES project_milestones(id),
    FOREIGN KEY (recurrent_activity_id) REFERENCES recurrent_activities(id),
    FOREIGN KEY (legislated_activity_id) REFERENCES legislated_activities(id)
);

CREATE TABLE assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE claims (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    status ENUM('Pending', 'Approved', 'Rejected') DEFAULT 'Pending',
    total_amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE claim_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    claim_id INT NOT NULL,
    workplan_activity_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (claim_id) REFERENCES claims(id),
    FOREIGN KEY (workplan_activity_id) REFERENCES workplan_activities(id)
);

CREATE TABLE claim_forms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    claim_id INT NOT NULL,
    form_type ENUM('FF3', 'FF4', 'Alignment Sheet') NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    version INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (claim_id) REFERENCES claims(id)
);

CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    workplan_activity_id INT NOT NULL,
    claim_id INT NULL,
    type ENUM('Report', 'Acquittal') NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (workplan_activity_id) REFERENCES workplan_activities(id),
    FOREIGN KEY (claim_id) REFERENCES claims(id)
);
```

---

### Additional Notes
1. **PDF Generation**:
   - Use a library like **DomPDF** to generate FF3, FF4, and Alignment Sheets.
   - Store generated PDFs in the `claim_forms` table with version tracking.
   - Alignment Sheets should query linked data (e.g., budget codes, plans, KPIs) to display organizational alignment.

2. **AJAX Implementation**:
   - Use jQuery with Bootstrap 5 for dynamic form updates (e.g., enabling/disabling amount fields based on budget code linkage).
   - Example: When selecting workplan activities for a claim, fetch budget code details via AJAX (`GET /api/workplan-activities/{id}/budget`).

3. **RESTful API**:
   - Secure endpoints with API tokens or CodeIgniter’s CSRF protection.
   - Return JSON responses with appropriate HTTP status codes (200, 201, 400, etc.).

4. **Role-Based Access**:
   - Implement middleware in CodeIgniter to restrict access based on user roles (e.g., `is_admin`, `is_fund_manager` in `positions` table).
   - Example: Only fund managers can access `/api/claims/{id}/workflow`.

5. **Mobile-Friendly Interface**:
   - Use Bootstrap 5’s grid system and button classes (e.g., `btn-primary`, `btn-lg`) for a mobile app-like feel.
   - Ensure buttons are large and touch-friendly, with minimal navigation clutter.

---

This design provides a comprehensive foundation for IPMS V1, covering all required functionality with a focus on MVC, CRUD, and RESTful principles. Let me know if you need further details or specific code snippets for any module!