<?php

namespace App\Controllers;

class <PERSON><PERSON><PERSON> extends BaseController
{
    public function index()
    {
        return view('dakoii/dakoii_login');
    }

    public function dashboard()
    {
        return view('dakoii/dakoii_dashboard');
    }

    public function organizations()
    {
        // Mock data for organizations
        $data['organizations'] = [
            [
                'id' => 1,
                'name' => 'Papua New Guinea Department of Planning',
                'code' => 'PNG-DOP',
                'status' => 'active',
                'admin_count' => 3,
                'user_count' => 45,
                'created_at' => '2024-01-15',
                'last_activity' => '2024-12-20'
            ],
            [
                'id' => 2,
                'name' => 'Ministry of Health',
                'code' => 'PNG-MOH',
                'status' => 'active',
                'admin_count' => 2,
                'user_count' => 28,
                'created_at' => '2024-02-10',
                'last_activity' => '2024-12-19'
            ],
            [
                'id' => 3,
                'name' => 'Department of Education',
                'code' => 'PNG-DOE',
                'status' => 'inactive',
                'admin_count' => 1,
                'user_count' => 12,
                'created_at' => '2024-03-05',
                'last_activity' => '2024-11-15'
            ]
        ];

        return view('dakoii/dakoii_organizations', $data);
    }

    public function admins()
    {
        // Mock data for organization admins
        $data['admins'] = [
            [
                'id' => 1,
                'name' => 'John Kila',
                'email' => '<EMAIL>',
                'organization' => 'Papua New Guinea Department of Planning',
                'role' => 'Super Admin',
                'status' => 'active',
                'last_login' => '2024-12-20 09:30:00',
                'created_at' => '2024-01-15'
            ],
            [
                'id' => 2,
                'name' => 'Mary Temu',
                'email' => '<EMAIL>',
                'organization' => 'Ministry of Health',
                'role' => 'Admin',
                'status' => 'active',
                'last_login' => '2024-12-19 14:15:00',
                'created_at' => '2024-02-10'
            ],
            [
                'id' => 3,
                'name' => 'Peter Namaliu',
                'email' => '<EMAIL>',
                'organization' => 'Department of Education',
                'role' => 'Admin',
                'status' => 'inactive',
                'last_login' => '2024-11-15 16:45:00',
                'created_at' => '2024-03-05'
            ]
        ];

        return view('dakoii/dakoii_admins', $data);
    }

    public function settings()
    {
        return view('dakoii/dakoii_settings');
    }

    public function reports()
    {
        // Mock data for reports
        $data['stats'] = [
            'total_organizations' => 15,
            'active_organizations' => 12,
            'total_admins' => 28,
            'active_admins' => 24,
            'total_users' => 456,
            'active_users' => 389,
            'system_uptime' => '99.8%',
            'avg_response_time' => '245ms'
        ];

        return view('dakoii/dakoii_reports', $data);
    }

    public function createOrganization()
    {
        return view('dakoii/dakoii_create_organization');
    }

    public function createAdmin()
    {
        return view('dakoii/dakoii_create_admin');
    }

    // Mock authentication method
    public function authenticate()
    {
        // This would normally handle actual authentication
        // For now, just redirect to dashboard
        return redirect()->to('dakoii/dashboard');
    }
}
