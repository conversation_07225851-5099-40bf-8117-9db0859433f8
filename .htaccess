# Disable directory browsing
Options -Indexes

# ----------------------------------------------------------------------
# Rewrite engine
# ----------------------------------------------------------------------

# Turning on the rewrite engine is necessary for the following rules and features.
<IfModule mod_rewrite.c>
    Options +FollowSymlinks
    RewriteEngine On

    # Handle Angular and other front-end routes - send to public folder
    # Redirect all requests to public folder
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ public/$1 [L]

    # Handle requests that go directly to public folder
    RewriteCond %{REQUEST_URI} ^/public/(.*)$
    RewriteRule ^public/(.*)$ $1 [R=301,L]
</IfModule>

# Disable server signature
ServerSignature Off 