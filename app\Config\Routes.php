<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
$routes->get('home', 'Home::index');
$routes->get('home/login', 'Home::login');
$routes->get('dashboard', 'DashboardController::index');

// Dakoii System Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->get('dakoii/dashboard', 'Dakoii::dashboard');
$routes->post('dakoii/authenticate', 'Dakoii::authenticate');
$routes->get('dakoii/organizations', 'Dakoii::organizations');
$routes->get('dakoii/organizations/create', 'Dakoii::createOrganization');
$routes->get('dakoii/admins', 'Dakoii::admins');
$routes->get('dakoii/admins/create', 'Dakoii::createAdmin');
$routes->get('dakoii/settings', 'Dakoii::settings');
$routes->get('dakoii/reports', 'Dakoii::reports');
