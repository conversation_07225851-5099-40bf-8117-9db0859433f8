<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Da<PERSON><PERSON>gin - Dakoii Systems</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">

    <!-- Bootstrap 5 CSS (Dark Theme) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --dakoii-dark-green: #1a4d3a;
            --dakoii-green: #28a745;
            --dakoii-light-green: #34ce57;
            --dakoii-dark-maroon: #4a0e0e;
            --dakoii-maroon: #800000;
            --dakoii-light-maroon: #a00000;
            --dakoii-bg-primary: #0d1117;
            --dakoii-bg-secondary: #161b22;
            --dakoii-bg-tertiary: #21262d;
            --dakoii-border: #30363d;
            --dakoii-text-primary: #f0f6fc;
            --dakoii-text-secondary: #8b949e;
        }

        body {
            background: linear-gradient(135deg, var(--dakoii-bg-primary) 0%, var(--dakoii-bg-secondary) 50%, var(--dakoii-bg-primary) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(40,167,69,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: 1;
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 450px;
            margin: 2rem;
        }

        .login-card {
            background: rgba(22, 27, 34, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid var(--dakoii-border);
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, var(--dakoii-dark-green), var(--dakoii-green));
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid var(--dakoii-border);
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            background-color: var(--dakoii-bg-tertiary);
            border: 1px solid var(--dakoii-border);
            color: var(--dakoii-text-primary);
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background-color: var(--dakoii-bg-tertiary);
            border-color: var(--dakoii-green);
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            color: var(--dakoii-text-primary);
        }

        .form-floating > label {
            color: var(--dakoii-text-secondary);
        }

        .btn-dakoii-login {
            background: linear-gradient(135deg, var(--dakoii-green), var(--dakoii-light-green));
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-dakoii-login:hover {
            background: linear-gradient(135deg, var(--dakoii-dark-green), var(--dakoii-green));
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .forgot-password {
            color: var(--dakoii-green);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--dakoii-light-green);
            text-decoration: underline;
        }

        .back-to-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            z-index: 3;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
            color: var(--dakoii-green);
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
            color: var(--dakoii-maroon);
        }

        .shape:nth-child(3) {
            bottom: 10%;
            left: 20%;
            animation-delay: 4s;
            color: var(--dakoii-green);
        }

        .shape:nth-child(4) {
            bottom: 20%;
            right: 20%;
            animation-delay: 1s;
            color: var(--dakoii-maroon);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(-40px) rotate(180deg); }
            75% { transform: translateY(-20px) rotate(270deg); }
        }

        .alert {
            border-radius: 0.5rem;
            border: none;
        }

        .alert-danger {
            background-color: rgba(128, 0, 0, 0.2);
            color: var(--dakoii-light-maroon);
            border: 1px solid var(--dakoii-maroon);
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.2);
            color: var(--dakoii-light-green);
            border: 1px solid var(--dakoii-green);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
            }

            .login-header,
            .login-body {
                padding: 1.5rem;
            }

            .back-to-home {
                top: 1rem;
                left: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back to Home Button -->
    <a href="<?= site_url('/') ?>" class="btn btn-outline-light back-to-home">
        <i class="fas fa-arrow-left me-2"></i>Back to Home
    </a>

    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <i class="fas fa-building shape" style="font-size: 3rem;"></i>
        <i class="fas fa-users-cog shape" style="font-size: 2.5rem;"></i>
        <i class="fas fa-chart-line shape" style="font-size: 3.5rem;"></i>
        <i class="fas fa-cog shape" style="font-size: 2rem;"></i>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <div class="login-card">
            <!-- Login Header -->
            <div class="login-header">
                <div class="login-logo">
                    <img src="<?= base_url('public/assets/system_images/dakoii-systems-logo.png') ?>" alt="Dakoii Systems" height="50">
                </div>
                <h2 class="fw-bold mb-2 text-white">Dakoii Login</h2>
                <p class="text-light mb-0 opacity-75">Access Dakoii Systems Administration Portal</p>
            </div>

            <!-- Login Body -->
            <div class="login-body">
                <!-- Error Alert (Hidden by default) -->
                <div class="alert alert-danger d-none" id="errorAlert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorMessage">Invalid credentials. Please try again.</span>
                </div>

                <!-- Success Alert (Hidden by default) -->
                <div class="alert alert-success d-none" id="successAlert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span id="successMessage">Login successful! Redirecting...</span>
                </div>

                <!-- Login Form -->
                <form id="loginForm" method="POST" action="<?= site_url('dakoii/authenticate') ?>">
                    <!-- Email Field -->
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                    </div>

                    <!-- Password Field -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                    </div>

                    <!-- Remember Me Checkbox -->
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                        <label class="form-check-label text-light" for="rememberMe">
                            Remember me
                        </label>
                    </div>

                    <!-- Login Button -->
                    <button type="submit" class="btn btn-dakoii-login mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span class="btn-text">Access Portal</span>
                    </button>

                    <!-- Forgot Password Link -->
                    <div class="text-center">
                        <a href="#" class="forgot-password">
                            <i class="fas fa-question-circle me-1"></i>Forgot your password?
                        </a>
                    </div>
                </form>

                <!-- Demo Credentials Info -->
                <div class="mt-4 p-3 rounded" style="background-color: var(--dakoii-bg-tertiary); border: 1px solid var(--dakoii-border);">
                    <h6 class="text-success mb-2"><i class="fas fa-info-circle me-2"></i>Demo Credentials</h6>
                    <small class="text-light">
                        <strong>Email:</strong> <EMAIL><br>
                        <strong>Password:</strong> dakoii123
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const errorAlert = document.getElementById('errorAlert');
            const successAlert = document.getElementById('successAlert');

            // Handle login form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const submitBtn = this.querySelector('button[type="submit"]');
                const btnText = submitBtn.querySelector('.btn-text');
                const originalText = btnText.textContent;

                // Show loading state
                btnText.innerHTML = '<span class="loading"></span> Authenticating...';
                submitBtn.disabled = true;

                // Hide any existing alerts
                errorAlert.classList.add('d-none');
                successAlert.classList.add('d-none');

                // Simulate authentication (replace with actual AJAX call)
                setTimeout(() => {
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;

                    // Demo credentials
                    if (email === '<EMAIL>' && password === 'dakoii123') {
                        // Success
                        successAlert.classList.remove('d-none');
                        setTimeout(() => {
                            window.location.href = '<?= site_url('dakoii/dashboard') ?>';
                        }, 1500);
                    } else {
                        // Error
                        errorAlert.classList.remove('d-none');
                        btnText.textContent = originalText;
                        submitBtn.disabled = false;
                    }
                }, 2000);
            });

            // Add floating animation to shapes
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.style.animationDelay = `${index * 0.5}s`;
            });

            // Add focus effects to form inputs
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
