<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('title') ?>Organizations<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Organizations Management<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h4 class="text-success mb-2">Organizations Management</h4>
            <p class="text-muted mb-0">Manage all organizations using the IPMS system. Create, edit, and monitor organizational activities.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?= site_url('dakoii/organizations/create') ?>" class="btn btn-dakoii-primary">
                <i class="fas fa-plus me-2"></i>Create Organization
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-building text-success fs-2 mb-3"></i>
                    <h4 class="text-success mb-1"><?= count($organizations) ?></h4>
                    <p class="text-muted mb-0">Total Organizations</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle text-success fs-2 mb-3"></i>
                    <h4 class="text-success mb-1"><?= count(array_filter($organizations, fn($org) => $org['status'] === 'active')) ?></h4>
                    <p class="text-muted mb-0">Active Organizations</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-info fs-2 mb-3"></i>
                    <h4 class="text-info mb-1"><?= array_sum(array_column($organizations, 'user_count')) ?></h4>
                    <p class="text-muted mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-user-cog fs-2 mb-3" style="color: #800000;"></i>
                    <h4 class="mb-1" style="color: #800000;"><?= array_sum(array_column($organizations, 'admin_count')) ?></h4>
                    <p class="text-muted mb-0">Total Admins</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Organizations Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0"><i class="fas fa-list me-2 text-success"></i>Organizations List</h5>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-dark border-secondary">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="Search organizations..." id="searchInput">
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-dark table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Organization</th>
                            <th>Code</th>
                            <th>Status</th>
                            <th>Admins</th>
                            <th>Users</th>
                            <th>Created</th>
                            <th>Last Activity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="organizationsTable">
                        <?php foreach ($organizations as $org): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        <i class="fas fa-building text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 text-light"><?= esc($org['name']) ?></h6>
                                        <small class="text-muted">ID: <?= $org['id'] ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?= esc($org['code']) ?></span>
                            </td>
                            <td>
                                <?php if ($org['status'] === 'active'): ?>
                                    <span class="badge badge-success">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times-circle me-1"></i>Inactive
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-light"><?= $org['admin_count'] ?></span>
                            </td>
                            <td>
                                <span class="text-light"><?= $org['user_count'] ?></span>
                            </td>
                            <td>
                                <span class="text-muted"><?= date('M j, Y', strtotime($org['created_at'])) ?></span>
                            </td>
                            <td>
                                <span class="text-muted"><?= date('M j, Y', strtotime($org['last_activity'])) ?></span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-success" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-muted">Showing <?= count($organizations) ?> organizations</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <nav aria-label="Organizations pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const tableBody = document.getElementById('organizationsTable');
        const rows = tableBody.querySelectorAll('tr');
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Action button handlers
        document.querySelectorAll('.btn-outline-success').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('View organization details functionality would be implemented here.');
            });
        });
        
        document.querySelectorAll('.btn-outline-primary').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Edit organization functionality would be implemented here.');
            });
        });
        
        document.querySelectorAll('.btn-outline-danger').forEach(btn => {
            btn.addEventListener('click', function() {
                if (confirm('Are you sure you want to delete this organization?')) {
                    alert('Delete organization functionality would be implemented here.');
                }
            });
        });
        
        console.log('Organizations page initialized successfully');
    });
</script>
<?= $this->endSection() ?>
