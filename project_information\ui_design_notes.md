### UI Design and Page Navigation Structure for IPMS (Updated)

The Integrated Progress Monitoring System (IPMS) UI is designed to be modern, mobile-friendly, and button-driven, using Bootstrap 5 for a responsive and consistent interface, DataTables for dynamic table management, and Chart.js for data visualizations. Based on prior conversations, IPMS manages human resources, organizational plans, budgets, and asset usage linked to Workplans and Annual Activity Plans, with a RESTful architecture, MVC structure, and role-based access control. This writeup updates the UI design to eliminate the sidebar, rely solely on buttons for navigation, and ensure consistency across pages with breadcrumbs, back buttons, and standardized button placement. The navigation flow starts from user login and covers all system features, tailored to user roles (super admins, admins, editors, officers, supervisors). No code is included, only a detailed writeup as requested.

---

### UI Design Overview

The IPMS UI is sleek, modern, and optimized for mobile and desktop use, leveraging Bootstrap 5’s responsive grid system, components, and utilities. The design replaces the sidebar with a button-driven navigation system, ensuring all interactions are intuitive and consistent. DataTables provides interactive tables with sorting, filtering, and pagination, while Chart.js delivers clear, responsive charts for data insights. The maroon and green color scheme, inspired by prior SMAS preferences, ensures visual consistency, with maroon as the primary color and green as the secondary for buttons and accents.

**Key UI Elements:**
- **Navbar**: A fixed-top Bootstrap 5 navbar with a maroon background, white text, and green hover effects. It includes the IPMS logo (left), a user profile dropdown (top-right for Profile Settings, Notifications, Logout), and a hamburger menu toggle for mobile responsiveness.
- **Breadcrumbs**: A Bootstrap 5 breadcrumb component at the top of every page (below the navbar), showing the navigation path (e.g., Dashboard > Plans Management > Workplan Details). Breadcrumbs use maroon for active links and gray for inactive ones.
- **Back Button**: A “Back” button with a left-arrow icon (Font Awesome `fa-arrow-left`) on every page except the dashboard, located at the top-right corner alongside other buttons. Styled in green to differentiate from primary maroon buttons.
- **Action Buttons**: All navigation and action buttons (e.g., “Create New,” “Generate Report”) are placed at the top-right of each page, using Bootstrap 5 button classes with maroon (`btn-primary`) for primary actions and green (`btn-secondary`) for secondary actions. Each button includes a Font Awesome icon (e.g., `fa-plus` for “Create New,” `fa-download` for “Export”).
- **Main Content Area**: A fluid container with Bootstrap 5 card-based layouts for module content. Cards include headers (maroon), body content (e.g., DataTables, forms, charts), and consistent top-right button groups for actions.
- **DataTables**: Tables use DataTables.js for lists (e.g., users, workplans, claims) with columns for key data and an “Actions” column with Bootstrap 5 dropdowns for CRUD operations (View, Edit, Delete). Dropdowns use maroon for the toggle and green for action items.
- **Charts**: Chart.js visualizations (e.g., bar, line, pie) display metrics like budget utilization or workplan progress, using maroon, green, and neutral tones (white, gray). Charts are responsive, adjusting to screen size.
- **Forms**: Bootstrap 5 forms with validation feedback for data entry (e.g., creating workplans, submitting claims). Forms use maroon “Submit” buttons and green “Save Draft” or “Cancel” buttons, all positioned top-right.
- **Modals**: Bootstrap 5 modals for confirmations, alerts, or detailed views (e.g., workplan milestones). Modals include maroon primary buttons and green secondary buttons, with a close icon (`fa-times`) top-right.
- **Footer**: A simple footer with system version, contact links, and copyright, styled with Bootstrap 5 utilities, fixed at the bottom for consistency across pages.

**Color Scheme**:
- Primary: Maroon (#800000) for navbar, card headers, and primary buttons.
- Secondary: Green (#28a745) for back buttons, secondary actions, and chart accents.
- Background: White (#ffffff) for light theme, with a dark theme option (#343a40).
- Accents: Light gray (#f8f9fa) for card backgrounds, dark gray (#6c757d) for text.

**Typography**:
- Font: Bootstrap 5’s default font stack (system fonts like -apple-system, Segoe UI) for performance.
- Headings: Bold, maroon-colored headers for cards and sections.
- Body Text: Dark gray for readability, with green for links and interactive elements.

**Button Consistency**:
- All buttons are located top-right in a Bootstrap 5 button group, ensuring uniform placement across pages.
- Buttons include descriptive names and Font Awesome icons (e.g., “Create New” with `fa-plus`, “Back” with `fa-arrow-left`).
- Primary actions (e.g., “Submit,” “Create New”) use maroon (`btn-primary`), secondary actions (e.g., “Back,” “Cancel”) use green (`btn-secondary`).
- Buttons are sized consistently using Bootstrap’s `btn-md` class for desktop and scale responsively for mobile.

**Responsiveness**:
- The layout uses Bootstrap 5’s grid system for mobile, tablet, and desktop compatibility.
- On mobile, the navbar collapses into a hamburger menu, and button groups stack vertically in the top-right corner.
- DataTables adjusts to a stacked view on smaller screens, with responsive column prioritization.
- Chart.js charts resize dynamically for all screen sizes.
- Breadcrumbs wrap gracefully on mobile, maintaining readability.

**Standard Element Positioning**:
- **Navbar**: Fixed at the top, with logo left and profile dropdown right.
- **Breadcrumbs**: Below navbar, left-aligned, on every page except the landing page.
- **Back Button**: Top-right, in a button group with other actions, on all pages except the dashboard.
- **Action Buttons**: Top-right, in a button group, consistent across all pages.
- **Content Cards**: Centered in the main container, with headers, content (tables/charts/forms), and top-right buttons.
- **Footer**: Fixed at the bottom, centered, on all authenticated pages.

---

### Page Navigation Structure

The navigation structure is button-driven, with all navigation originating from the dashboard. Each page includes breadcrumbs for context and a “Back” button to return to the previous page (typically the dashboard or a parent module). The structure is organized by modules, with role-based access for super admins, admins, editors, officers, and supervisors.

**1. Landing Page (Public)**
- **Purpose**: Introduce IPMS to unauthenticated users.
- **Content**: Hero section with a maroon-to-green gradient background, tagline (“Streamline Progress, Empower Performance”), and a “Login” button (maroon, with `fa-sign-in-alt` icon) top-right. Includes feature cards and a footer.
- **Navigation**: “Login” button links to the login page.
- **Breadcrumbs**: None (public page).
- **Buttons**: “Login” (top-right, maroon, `fa-sign-in-alt`).

**2. Login Page**
- **Purpose**: Authenticate users.
- **Content**: Centered Bootstrap 5 card with a login form (email, password), a maroon “Login” button (`fa-sign-in-alt`), and a “Forgot Password” link (green, `fa-question-circle`). Uses a maroon-green gradient background.
- **Navigation**: Submits to the dashboard on successful login; displays an error for invalid credentials.
- **Breadcrumbs**: None (entry point).
- **Buttons**: “Login” (top-right, maroon, `fa-sign-in-alt`), “Forgot Password” (top-right, green, `fa-question-circle`).

**3. Dashboard (Authenticated Users)**
- **Purpose**: Central hub with role-based summaries and navigation to all modules.
- **Content**:
  - Welcome message with user’s name and role.
  - Summary cards with metrics (e.g., active workplans, pending claims) and green “View Details” buttons (`fa-eye`).
  - Chart.js bar chart for budget utilization or workplan progress.
  - DataTable listing recent activities/tasks with an “Actions” dropdown (maroon toggle, green items, `fa-cog`).
  - Top-right button group for module navigation.
- **Navigation**:
  - Buttons for “User Management” (super admins/admins only, `fa-users`), “Structure Management” (`fa-sitemap`), “Plans Management” (`fa-clipboard-list`), “Financial Claims” (`fa-dollar-sign`), and “Reports” (`fa-chart-bar`).
  - Navbar dropdown for “Profile Settings” (`fa-user`), “Notifications” (`fa-bell`), and “Logout” (`fa-sign-out-alt`).
- **Breadcrumbs**: “Dashboard” (active).
- **Buttons**: “User Management” (`fa-users`), “Structure Management” (`fa-sitemap`), “Plans Management” (`fa-clipboard-list`), “Financial Claims” (`fa-dollar-sign`), “Reports” (`fa-chart-bar`) (all top-right, maroon).

**4. User Management Module**
- **Purpose**: Manage users, roles, and permissions (super admins/admins only).
- **Content**:
  - DataTable listing users (Name, Email, Role, Organization, Actions).
  - Bootstrap 5 modal for adding/editing users (email, password, role, organization).
  - Chart.js pie chart for role distribution.
- **Navigation**:
  - “Back” button to dashboard.
  - “Create New” button opens a modal to add users.
  - “Export Data” button (`fa-download`) for CSV/PDF export.
  - Table dropdowns for View (`fa-eye`), Edit (`fa-edit`), Delete (`fa-trash`).
- **Breadcrumbs**: “Dashboard > User Management” (User Management active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Create New” (maroon, `fa-plus`), “Export Data” (maroon, `fa-download`) (all top-right).

**5. Structure Management Module**
- **Purpose**: Manage organizational structures, position groups, and budget codes.
- **Content**:
  - DataTable listing organizations/position groups (Name, Code, Status, Actions).
  - Modals for creating/editing structures (name, code, budget links).
  - Chart.js line chart for structure changes over time.
- **Navigation**:
  - “Back” button to dashboard.
  - “Create New” button opens a modal for new structures.
  - Table dropdowns for View (`fa-eye`), Edit (`fa-edit`), Delete (`fa-trash`).
- **Breadcrumbs**: “Dashboard > Structure Management” (Structure Management active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Create New” (maroon, `fa-plus`) (all top-right).

**6. Plans Management Module**
- **Purpose**: Manage Workplans, Activities, and Annual Activity Plans.
- **Content**:
  - DataTable listing workplans (Title, Officer, Status, Budget Code, Actions).
  - Detailed view for workplans (activities, milestones, budget) accessed via “View” dropdown.
  - Forms in modals for creating/editing workplans (activity types: Projects, Recurrent, Legislated; assignees; codes).
  - Chart.js bar chart for completion rates.
- **Navigation**:
  - “Back” button to dashboard.
  - “Create New” button opens a modal for workplans.
  - “Generate Report” button (`fa-chart-bar`) for workplan reports.
  - Table dropdowns for View (`fa-eye`), Edit (`fa-edit`), Delete (`fa-trash`).
- **Breadcrumbs**: “Dashboard > Plans Management” (Plans Management active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Create New” (maroon, `fa-plus`), “Generate Report” (maroon, `fa-chart-bar`) (all top-right).

**7. Financial Claims Module**
- **Purpose**: Manage financial claims and generate forms (FF3, FF4, Alignment Sheets).
- **Content**:
  - DataTable listing claims (Claim ID, Amount, Status, Workplan, Actions).
  - Forms in modals for submitting claims (linked to workplans/budgets).
  - Section for generating FF3, FF4, or Alignment Sheets as PDFs.
  - Chart.js doughnut chart for claim status distribution.
- **Navigation**:
  - “Back” button to dashboard.
  - “Submit Claim” button opens a modal for new claims.
  - “Generate Form” button (`fa-file-pdf`) for downloading forms.
  - Table dropdowns for View (`fa-eye`), Edit (`fa-edit`), Approve/Reject (`fa-check`/`fa-times`).
- **Breadcrumbs**: “Dashboard > Financial Claims” (Financial Claims active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Submit Claim” (maroon, `fa-plus`), “Generate Form” (maroon, `fa-file-pdf`) (all top-right).

**8. Reports Module**
- **Purpose**: Generate and view reports on workplans, budgets, and performance.
- **Content**:
  - Filters for report criteria (e.g., date range, module).
  - Chart.js visualizations (e.g., line chart for progress, pie chart for budgets).
  - DataTable listing reports with download options (PDF/Excel).
- **Navigation**:
  - “Back” button to dashboard.
  - “Generate Report” button creates new reports.
  - “Export All” button (`fa-download`) for bulk export.
- **Breadcrumbs**: “Dashboard > Reports” (Reports active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Generate Report” (maroon, `fa-chart-bar`), “Export All” (maroon, `fa-download`) (all top-right).

**9. Profile Settings**
- **Purpose**: Update user profile and preferences.
- **Content**: Form for name, email, password, and notification settings.
- **Navigation**: “Back” button to dashboard; “Save” button to update profile.
- **Breadcrumbs**: “Dashboard > Profile Settings” (Profile Settings active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Save” (maroon, `fa-save`) (all top-right).

**10. Notifications**
- **Purpose**: View system alerts (e.g., new tasks, claim approvals).
- **Content**: List of notifications with timestamps and actions (e.g., Mark as Read).
- **Navigation**: “Back” button to dashboard; “Mark All Read” button.
- **Breadcrumbs**: “Dashboard > Notifications” (Notifications active).
- **Buttons**: “Back” (green, `fa-arrow-left`), “Mark All Read” (maroon, `fa-check-circle`) (all top-right).

**11. Logout**
- **Purpose**: End user session.
- **Content**: None (automatic redirect).
- **Navigation**: Redirects to login page.
- **Breadcrumbs**: None.
- **Buttons**: “Logout” (maroon, `fa-sign-out-alt`, in navbar dropdown).

---

### System User Navigation Flow

The user navigation flow starts at login and progresses through a button-driven interface, with all navigation originating from the dashboard. Each page includes breadcrumbs and a “Back” button for intuitive movement, with consistent top-right button placement.

**1. User Login**
- From the landing page, click “Login” (maroon, `fa-sign-in-alt`, top-right).
- On the login page, enter email and password in a Bootstrap 5 form.
- Click “Login” (maroon, `fa-sign-in-alt`, top-right) to access the dashboard or “Forgot Password” (green, `fa-question-circle`, top-right) for recovery.
- Successful login redirects to the dashboard; invalid credentials show an error.

**2. Dashboard Access**
- The dashboard displays role-specific content (e.g., super admins see user metrics, officers see assigned workplans).
- Summary cards show metrics (e.g., pending tasks) with “View Details” buttons (green, `fa-eye`).
- A Chart.js bar chart visualizes budget or progress data.
- A DataTable lists recent activities with “Actions” dropdowns (maroon toggle, green items, `fa-cog`).
- Top-right buttons navigate to modules: “User Management” (`fa-users`, super admins/admins only), “Structure Management” (`fa-sitemap`), “Plans Management” (`fa-clipboard-list`), “Financial Claims” (`fa-dollar-sign`), “Reports” (`fa-chart-bar`).
- Navbar dropdown accesses “Profile Settings” (`fa-user`), “Notifications” (`fa-bell`), and “Logout” (`fa-sign-out-alt`).

**3. Navigating Modules**
- **User Management (Super Admins/Admins)**:
  - From dashboard, click “User Management” (maroon, `fa-users`, top-right).
  - Breadcrumbs: “Dashboard > User Management.”
  - View user list in DataTable; use dropdowns for View (`fa-eye`), Edit (`fa-edit`), Delete (`fa-trash`).
  - Click “Create New” (maroon, `fa-plus`, top-right) to open a modal for adding users.
  - Click “Export Data” (maroon, `fa-download`, top-right) for CSV/PDF.
  - View pie chart for role distribution.
  - Click “Back” (green, `fa-arrow-left`, top-right) to return to dashboard.
- **Structure Management**:
  - From dashboard, click “Structure Management” (maroon, `fa-sitemap`, top-right).
  - Breadcrumbs: “Dashboard > Structure Management.”
  - Browse organizations/position groups in DataTable; use dropdowns for CRUD actions.
  - Click “Create New” (maroon, `fa-plus`, top-right) for new structures.
  - View line chart for structure changes.
  - Click “Back” (green, `fa-arrow-left`, top-right) to dashboard.
- **Plans Management**:
  - From dashboard, click “Plans Management” (maroon, `fa-clipboard-list`, top-right).
  - Breadcrumbs: “Dashboard > Plans Management.”
  - View workplans in DataTable; use dropdowns for View (`fa-eye`), Edit (`fa-edit`), Delete (`fa-trash`).
  - Click “Create New” (maroon, `fa-plus`, top-right) to create workplans.
  - Click “Generate Report” (maroon, `fa-chart-bar`, top-right) for reports.
  - View bar chart for completion rates.
  - Click “Back” (green, `fa-arrow-left`, top-right) to dashboard.
- **Financial Claims**:
  - From dashboard, click “Financial Claims” (maroon, `fa-dollar-sign`, top-right).
  - Breadcrumbs: “Dashboard > Financial Claims.”
  - View claims in DataTable; use dropdowns for View (`fa-eye`), Edit (`fa-edit`), Approve/Reject (`fa-check`/`fa-times`).
  - Click “Submit Claim” (maroon, `fa-plus`, top-right) for new claims.
  - Click “Generate Form” (maroon, `fa-file-pdf`, top-right) for FF3/FF4/Alignment Sheets.
  - View doughnut chart for claim statuses.
  - Click “Back” (green, `fa-arrow-left`, top-right) to dashboard.
- **Reports**:
  - From dashboard, click “Reports” (maroon, `fa-chart-bar`, top-right).
  - Breadcrumbs: “Dashboard > Reports.”
  - Use filters to generate reports; view in DataTable with download options.
  - Click “Generate Report” (maroon, `fa-chart-bar`, top-right) for new reports.
  - Click “Export All” (maroon, `fa-download`, top-right) for bulk export.
  - View line/pie charts for trends.
  - Click “Back” (green, `fa-arrow-left`, top-right) to dashboard.

**4. Profile and Notifications**
- From navbar dropdown, select “Profile Settings” (`fa-user`) to update details.
  - Breadcrumbs: “Dashboard > Profile Settings.”
  - Click “Save” (maroon, `fa-save`, top-right) or “Back” (green, `fa-arrow-left`, top-right) to dashboard.
- Select “Notifications” (`fa-bell`) to view alerts.
  - Breadcrumbs: “Dashboard > Notifications.”
  - Click “Mark All Read” (maroon, `fa-check-circle`, top-right) or “Back” (green, `fa-arrow-left`, top-right) to dashboard.

**5. Logout**
- From navbar dropdown, select “Logout” (maroon, `fa-sign-out-alt`) to return to the login page.

**Role-Based Restrictions**:
- Super Admins: Access all modules, including User Management.
- Admins: Access Structure Management, Plans Management, Financial Claims, Reports; limited User Management (no super admin creation).
- Editors: Edit workplans/claims; read-only Reports.
- Officers: Manage assigned workplans/claims; view Reports.
- Supervisors: Approve claims, monitor workplans; read-only Reports.

**Error Handling and Feedback**:
- Invalid actions (e.g., accessing restricted modules) trigger Bootstrap 5 alerts.
- Form submissions show validation feedback (e.g., red highlights for errors).
- Modals confirm actions (e.g., “User Created”) or errors, with maroon “Confirm” and green “Cancel” buttons.

---

### Summary

The updated IPMS UI design is modern, mobile-friendly, and button-driven, eliminating the sidebar and using top-right button groups for all navigation and actions. Bootstrap 5 ensures responsiveness, DataTables manages dynamic tables, and Chart.js provides clear visualizations. The maroon-green color scheme maintains consistency, with standardized element positions (navbar top, breadcrumbs below, buttons top-right, cards centered, footer bottom). Breadcrumbs and “Back” buttons on every page (except dashboard) ensure intuitive navigation, starting from the dashboard. The user flow covers login to all features, respecting role-based restrictions and aligning with prior specifications for workplans, budgets, and form generation. This design delivers a streamlined, user-centric experience for IPMS.


# IPMS UI Design and Page Navigation Structure

## UI Design Overview
The IPMS UI is modern, mobile-friendly, and button-driven, using Bootstrap 5 for responsiveness, DataTables for tables, and Chart.js for visualizations. It eliminates the sidebar, relying on top-right button groups for navigation. The maroon (#800000) and green (#28a745) color scheme ensures consistency, with standardized element positions across pages.

### Key UI Elements
- **Navbar**: Fixed-top, maroon, with logo (left) and profile dropdown (top-right: Profile Settings, Notifications, Logout).
- **Breadcrumbs**: Left-aligned below navbar, showing navigation path (e.g., Dashboard > Plans Management).
- **Back Button**: Top-right, green, with `fa-arrow-left` icon, on all pages except dashboard.
- **Action Buttons**: Top-right, maroon (primary, e.g., “Create New” with `fa-plus`) or green (secondary, e.g., “Back”).
- **Content Cards**: Centered, with maroon headers, containing DataTables, forms, or charts.
- **DataTables**: Lists with sorting/filtering; “Actions” dropdowns (maroon toggle, green items).
- **Charts**: Chart.js visualizations (bar, line, pie) in maroon, green, and neutral tones.
- **Forms**: Bootstrap 5 forms with maroon “Submit” and green “Cancel” buttons (top-right).
- **Modals**: For confirmations/details, with maroon/green buttons.
- **Footer**: Fixed bottom, with version and contact info.

### Color Scheme
- Primary: Maroon (#800000).
- Secondary: Green (#28a745).
- Background: White (#ffffff) or dark (#343a40).
- Accents: Light gray (#f8f9fa), dark gray (#6c757d).

### Typography
- Font: Bootstrap 5 default (system fonts).
- Headings: Bold, maroon.
- Body: Dark gray; green for links.

### Responsiveness
- Bootstrap 5 grid for mobile/tablet/desktop.
- Mobile: Navbar collapses, buttons stack, DataTables adjust, charts resize.

### Standard Element Positioning
- Navbar: Top, logo left, dropdown right.
- Breadcrumbs: Below navbar, left.
- Buttons: Top-right, in button group.
- Cards: Centered.
- Footer: Bottom, centered.

## Page Navigation Structure
Navigation is button-driven, starting from the dashboard, with breadcrumbs and “Back” buttons.

### 1. Landing Page (Public)
- **Content**: Hero with maroon-green gradient, tagline, “Login” button.
- **Navigation**: “Login” to login page.
- **Breadcrumbs**: None.
- **Buttons**: “Login” (maroon, `fa-sign-in-alt`, top-right).

### 2. Login Page
- **Content**: Centered form, maroon “Login” button, green “Forgot Password” link.
- **Navigation**: Submits to dashboard or shows error.
- **Breadcrumbs**: None.
- **Buttons**: “Login” (maroon, `fa-sign-in-alt`), “Forgot Password” (green, `fa-question-circle`) (top-right).

### 3. Dashboard
- **Content**: Role-based cards, Chart.js bar chart, DataTable for activities.
- **Navigation**: Buttons to modules; navbar dropdown for Profile, Notifications, Logout.
- **Breadcrumbs**: “Dashboard” (active).
- **Buttons**: “User Management” (`fa-users`), “Structure Management” (`fa-sitemap`), “Plans Management” (`fa-clipboard-list`), “Financial Claims” (`fa-dollar-sign`), “Reports” (`fa-chart-bar`) (maroon, top-right).

### 4. User Management (Super Admins/Admins)
- **Content**: DataTable for users, modal for add/edit, pie chart.
- **Navigation**: “Back” to dashboard, “Create New” for users, “Export Data” for CSV/PDF.
- **Breadcrumbs**: “Dashboard > User Management.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Create New” (maroon, `fa-plus`), “Export Data” (maroon, `fa-download`) (top-right).

### 5. Structure Management
- **Content**: DataTable for structures, modal for add/edit, line chart.
- **Navigation**: “Back” to dashboard, “Create New” for structures.
- **Breadcrumbs**: “Dashboard > Structure Management.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Create New” (maroon, `fa-plus`) (top-right).

### 6. Plans Management
- **Content**: DataTable for workplans, modal for add/edit, bar chart.
- **Navigation**: “Back” to dashboard, “Create New” for workplans, “Generate Report.”
- **Breadcrumbs**: “Dashboard > Plans Management.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Create New” (maroon, `fa-plus`), “Generate Report” (maroon, `fa-chart-bar`) (top-right).

### 7. Financial Claims
- **Content**: DataTable for claims, modal for submissions, form generation, doughnut chart.
- **Navigation**: “Back” to dashboard, “Submit Claim,” “Generate Form.”
- **Breadcrumbs**: “Dashboard > Financial Claims.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Submit Claim” (maroon, `fa-plus`), “Generate Form” (maroon, `fa-file-pdf`) (top-right).

### 8. Reports
- **Content**: Filters, DataTable for reports, line/pie charts.
- **Navigation**: “Back” to dashboard, “Generate Report,” “Export All.”
- **Breadcrumbs**: “Dashboard > Reports.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Generate Report” (maroon, `fa-chart-bar`), “Export All” (maroon, `fa-download`) (top-right).

### 9. Profile Settings
- **Content**: Form for profile updates.
- **Navigation**: “Back” to dashboard, “Save.”
- **Breadcrumbs**: “Dashboard > Profile Settings.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Save” (maroon, `fa-save`) (top-right).

### 10. Notifications
- **Content**: Notification list.
- **Navigation**: “Back” to dashboard, “Mark All Read.”
- **Breadcrumbs**: “Dashboard > Notifications.”
- **Buttons**: “Back” (green, `fa-arrow-left`), “Mark All Read” (maroon, `fa-check-circle`) (top-right).

### 11. Logout
- **Content**: None (redirects to login).
- **Navigation**: Via navbar dropdown.
- **Breadcrumbs**: None.
- **Buttons**: “Logout” (maroon, `fa-sign-out-alt`, navbar dropdown).

## User Navigation Flow
### 1. Login
- Click “Login” on landing page, submit credentials, access dashboard or see error.

### 2. Dashboard
- View role-specific cards, chart, DataTable.
- Navigate to modules via top-right buttons or access Profile/Notifications/Logout via navbar.

### 3. Modules
- **User Management**: Click “User Management,” manage users, return via “Back.”
- **Structure Management**: Click “Structure Management,” manage structures, return via “Back.”
- **Plans Management**: Click “Plans Management,” manage workplans, return via “Back.”
- **Financial Claims**: Click “Financial Claims,” manage claims, generate forms, return via “Back.”
- **Reports**: Click “Reports,” generate/view reports, return via “Back.”

### 4. Profile/Notifications
- Access via navbar dropdown, update profile or view notifications, return via “Back.”

### 5. Logout
- Select “Logout” from navbar dropdown to return to login page.

### Role-Based Restrictions
- Super Admins: All modules.
- Admins: All except full User Management.
- Editors: Edit workplans/claims, view Reports.
- Officers: Manage assigned workplans/claims, view Reports.
- Supervisors: Approve claims, monitor workplans, view Reports.

### Error Handling
- Alerts for invalid actions.
- Form validation feedback.
- Modal confirmations for actions/errors.

## Summary
The IPMS UI is button-driven, mobile-friendly, and consistent, with top-right buttons, breadcrumbs, and “Back” navigation. It aligns with prior specifications, ensuring a streamlined experience.
