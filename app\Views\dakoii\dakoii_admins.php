<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('title') ?>Organization Admins<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Organization Admins Management<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h4 class="text-success mb-2">Organization Admins Management</h4>
            <p class="text-muted mb-0">Manage administrators for all organizations. Create, edit, and monitor admin accounts and permissions.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?= site_url('dakoii/admins/create') ?>" class="btn btn-dakoii-primary">
                <i class="fas fa-user-plus me-2"></i>Add New Admin
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-users-cog text-success fs-2 mb-3"></i>
                    <h4 class="text-success mb-1"><?= count($admins) ?></h4>
                    <p class="text-muted mb-0">Total Admins</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check text-success fs-2 mb-3"></i>
                    <h4 class="text-success mb-1"><?= count(array_filter($admins, fn($admin) => $admin['status'] === 'active')) ?></h4>
                    <p class="text-muted mb-0">Active Admins</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-crown fs-2 mb-3" style="color: #800000;"></i>
                    <h4 class="mb-1" style="color: #800000;"><?= count(array_filter($admins, fn($admin) => $admin['role'] === 'Super Admin')) ?></h4>
                    <p class="text-muted mb-0">Super Admins</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-clock text-info fs-2 mb-3"></i>
                    <h4 class="text-info mb-1"><?= count(array_filter($admins, fn($admin) => strtotime($admin['last_login']) > strtotime('-24 hours'))) ?></h4>
                    <p class="text-muted mb-0">Active Today</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Admins Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0"><i class="fas fa-list me-2 text-success"></i>Administrators List</h5>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-dark border-secondary">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="Search administrators..." id="searchInput">
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-dark table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Administrator</th>
                            <th>Organization</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="adminsTable">
                        <?php foreach ($admins as $admin): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 text-light"><?= esc($admin['name']) ?></h6>
                                        <small class="text-muted"><?= esc($admin['email']) ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-light"><?= esc($admin['organization']) ?></span>
                            </td>
                            <td>
                                <?php if ($admin['role'] === 'Super Admin'): ?>
                                    <span class="badge" style="background-color: #800000;">
                                        <i class="fas fa-crown me-1"></i><?= esc($admin['role']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-user-cog me-1"></i><?= esc($admin['role']) ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($admin['status'] === 'active'): ?>
                                    <span class="badge badge-success">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times-circle me-1"></i>Inactive
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-muted"><?= date('M j, Y H:i', strtotime($admin['last_login'])) ?></span>
                                <?php if (strtotime($admin['last_login']) > strtotime('-1 hour')): ?>
                                    <small class="text-success d-block">
                                        <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>Online
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-muted"><?= date('M j, Y', strtotime($admin['created_at'])) ?></span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-success" title="View Profile">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" title="Reset Password">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="Deactivate">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-muted">Showing <?= count($admins) ?> administrators</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <nav aria-label="Admins pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const tableBody = document.getElementById('adminsTable');
        const rows = tableBody.querySelectorAll('tr');
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Action button handlers
        document.querySelectorAll('.btn-outline-success').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('View admin profile functionality would be implemented here.');
            });
        });
        
        document.querySelectorAll('.btn-outline-primary').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Edit admin functionality would be implemented here.');
            });
        });
        
        document.querySelectorAll('.btn-outline-warning').forEach(btn => {
            btn.addEventListener('click', function() {
                if (confirm('Are you sure you want to reset this admin\'s password?')) {
                    alert('Reset password functionality would be implemented here.');
                }
            });
        });
        
        document.querySelectorAll('.btn-outline-danger').forEach(btn => {
            btn.addEventListener('click', function() {
                if (confirm('Are you sure you want to deactivate this admin account?')) {
                    alert('Deactivate admin functionality would be implemented here.');
                }
            });
        });
        
        console.log('Admins page initialized successfully');
    });
</script>
<?= $this->endSection() ?>
