/* IPMS Landing Page Styles */

:root {
    --primary-color: #28a745;    /* Green */
    --secondary-color: #800000;  /* Maroon */
    --light-bg: #f8f9fa;
    --dark-text: #343a40;
    --muted-text: #6c757d;
    --white: #ffffff;
}

/* Global Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 50%, var(--primary-color) 100%);
    position: relative;
    overflow: hidden;
}

.hero-overlay {
    background: rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    z-index: 1;
}

.hero-image {
    text-align: center;
    position: relative;
}

.hero-icon {
    font-size: 15rem;
    color: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Feature Cards */
.feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 2rem;
}

.feature-card h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--muted-text);
    margin-bottom: 0;
}

/* About Section */
.about-image {
    position: relative;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 2rem;
}

.stat-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.stat-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item h3 {
    color: var(--secondary-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.stat-item p {
    color: var(--muted-text);
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Module Cards */
.module-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #e9ecef;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.module-card i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    display: block;
}

.module-card h5 {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.module-card p {
    color: var(--muted-text);
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-outline-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}

/* Section Spacing */
section {
    padding: 5rem 0;
}

/* Planning Hierarchy Section */
.planning-hierarchy {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.hierarchy-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    border-left: 4px solid var(--secondary-color);
    transition: all 0.3s ease;
}

.hierarchy-item:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.hierarchy-item h5 {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.hierarchy-item p {
    color: var(--muted-text);
    margin-bottom: 0;
    font-size: 0.9rem;
}

.hierarchy-connector {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
}

.hierarchy-connector i {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-icon {
        font-size: 8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .feature-card,
    .module-card {
        padding: 1.5rem;
    }

    section {
        padding: 3rem 0;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-icon {
        font-size: 6rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-item i {
        font-size: 2rem;
    }

    .module-card i {
        font-size: 2.5rem;
    }

    .btn-lg {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a00000;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Focus States for Accessibility */
.btn:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }

    .hero-section {
        background: var(--white) !important;
        color: var(--dark-text) !important;
    }

    .hero-section * {
        color: var(--dark-text) !important;
    }
}
