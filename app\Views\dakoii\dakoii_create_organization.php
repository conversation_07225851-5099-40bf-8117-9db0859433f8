<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('title') ?>Create Organization<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Create New Organization<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h4 class="text-success mb-2">Create New Organization</h4>
            <p class="text-muted mb-0">Add a new organization to the IPMS system. Fill in all required information to set up the organization profile.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?= site_url('dakoii/organizations') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Organizations
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Organization Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-building me-2 text-success"></i>Organization Information</h5>
                </div>
                <div class="card-body">
                    <form id="createOrganizationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="orgName" class="form-label">Organization Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="orgName" name="org_name" required>
                                <div class="form-text">Full official name of the organization</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="orgCode" class="form-label">Organization Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="orgCode" name="org_code" required>
                                <div class="form-text">Unique identifier code (e.g., PNG-DOE)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="orgType" class="form-label">Organization Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="orgType" name="org_type" required>
                                    <option value="">Select Type</option>
                                    <option value="government">Government Department</option>
                                    <option value="ministry">Ministry</option>
                                    <option value="agency">Government Agency</option>
                                    <option value="corporation">State Corporation</option>
                                    <option value="ngo">NGO</option>
                                    <option value="private">Private Organization</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="orgSector" class="form-label">Sector <span class="text-danger">*</span></label>
                                <select class="form-select" id="orgSector" name="org_sector" required>
                                    <option value="">Select Sector</option>
                                    <option value="planning">Planning & Development</option>
                                    <option value="health">Health</option>
                                    <option value="education">Education</option>
                                    <option value="finance">Finance</option>
                                    <option value="infrastructure">Infrastructure</option>
                                    <option value="agriculture">Agriculture</option>
                                    <option value="environment">Environment</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="orgDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="orgDescription" name="org_description" rows="3"></textarea>
                            <div class="form-text">Brief description of the organization's purpose and activities</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="orgEmail" class="form-label">Official Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="orgEmail" name="org_email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="orgPhone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="orgPhone" name="org_phone">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="orgAddress" class="form-label">Address</label>
                            <textarea class="form-control" id="orgAddress" name="org_address" rows="2"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="orgWebsite" class="form-label">Website</label>
                                <input type="url" class="form-control" id="orgWebsite" name="org_website">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="orgStatus" class="form-label">Initial Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="orgStatus" name="org_status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-dakoii-primary">
                                <i class="fas fa-save me-2"></i>Create Organization
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2 text-info"></i>Help & Guidelines</h6>
                </div>
                <div class="card-body">
                    <h6 class="text-success">Organization Code Format</h6>
                    <p class="small text-muted mb-3">Use the format: COUNTRY-ABBREVIATION<br>
                    Examples: PNG-DOE, PNG-MOH, PNG-DOP</p>

                    <h6 class="text-success">Required Information</h6>
                    <ul class="small text-muted mb-3">
                        <li>Organization Name</li>
                        <li>Unique Organization Code</li>
                        <li>Organization Type</li>
                        <li>Sector Classification</li>
                        <li>Official Email Address</li>
                    </ul>

                    <h6 class="text-success">Next Steps</h6>
                    <p class="small text-muted mb-0">After creating the organization, you can:</p>
                    <ul class="small text-muted">
                        <li>Add organization administrators</li>
                        <li>Configure system settings</li>
                        <li>Set up user groups and permissions</li>
                        <li>Import existing users</li>
                    </ul>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-eye me-2 text-warning"></i>Organization Preview</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
                            <i class="fas fa-building text-white fs-4"></i>
                        </div>
                    </div>
                    <div id="previewContent">
                        <h6 class="text-center text-muted">Organization Preview</h6>
                        <p class="text-center small text-muted">Fill in the form to see a preview of the organization profile.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('createOrganizationForm');
        const previewContent = document.getElementById('previewContent');
        
        // Form validation and preview update
        function updatePreview() {
            const name = document.getElementById('orgName').value;
            const code = document.getElementById('orgCode').value;
            const type = document.getElementById('orgType').value;
            const sector = document.getElementById('orgSector').value;
            
            if (name || code || type || sector) {
                previewContent.innerHTML = `
                    <h6 class="text-center text-light">${name || 'Organization Name'}</h6>
                    <p class="text-center small text-muted mb-2">${code || 'ORG-CODE'}</p>
                    <div class="text-center">
                        <span class="badge bg-secondary small">${type || 'Type'}</span>
                        <span class="badge bg-info small">${sector || 'Sector'}</span>
                    </div>
                `;
            }
        }
        
        // Add event listeners for preview update
        ['orgName', 'orgCode', 'orgType', 'orgSector'].forEach(id => {
            document.getElementById(id).addEventListener('input', updatePreview);
        });
        
        // Auto-generate organization code
        document.getElementById('orgName').addEventListener('input', function() {
            const name = this.value;
            const codeField = document.getElementById('orgCode');
            
            if (name && !codeField.value) {
                // Simple code generation logic
                const words = name.split(' ');
                let code = 'PNG-';
                
                if (words.length >= 2) {
                    code += words.map(word => word.charAt(0).toUpperCase()).join('').substring(0, 3);
                } else {
                    code += name.substring(0, 3).toUpperCase();
                }
                
                codeField.value = code;
                updatePreview();
            }
        });
        
        // Form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                alert('Organization created successfully! This would normally save to the database.');
                
                // Reset form or redirect
                if (confirm('Would you like to create another organization?')) {
                    form.reset();
                    previewContent.innerHTML = `
                        <h6 class="text-center text-muted">Organization Preview</h6>
                        <p class="text-center small text-muted">Fill in the form to see a preview of the organization profile.</p>
                    `;
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                } else {
                    window.location.href = '<?= site_url('dakoii/organizations') ?>';
                }
            }, 2000);
        });
        
        console.log('Create Organization form initialized successfully');
    });
</script>
<?= $this->endSection() ?>
