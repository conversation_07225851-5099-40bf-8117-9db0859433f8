<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - IPMS</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('public/assets/system_images/favicon.ico') ?>">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= base_url('public/assets/themes/home_themes/css/style.css') ?>" rel="stylesheet">

    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 50%, #28a745 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: 1;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
            max-width: 450px;
            width: 100%;
            margin: 2rem;
        }

        .login-header {
            text-align: center;
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #800000, #a00000);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(128, 0, 0, 0.3);
            background: linear-gradient(135deg, #a00000, #800000);
        }

        .back-to-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            z-index: 3;
        }

        .forgot-password {
            color: #28a745;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #1e7e34;
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 10%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            bottom: 20%;
            right: 20%;
            animation-delay: 1s;
        }
    </style>
</head>
<body>
    <!-- Back to Home Button -->
    <a href="<?= site_url('home') ?>" class="btn btn-outline-light back-to-home">
        <i class="fas fa-arrow-left me-2"></i>Back to Home
    </a>

    <!-- Login Container -->
    <div class="login-container">
        <!-- Floating Shapes -->
        <div class="floating-shapes">
            <i class="fas fa-chart-line shape" style="font-size: 3rem;"></i>
            <i class="fas fa-users shape" style="font-size: 2.5rem;"></i>
            <i class="fas fa-clipboard-list shape" style="font-size: 3.5rem;"></i>
            <i class="fas fa-dollar-sign shape" style="font-size: 2rem;"></i>
        </div>

        <!-- Login Card -->
        <div class="login-card">
            <!-- Login Header -->
            <div class="login-header">
                <div class="login-logo">
                    <img src="<?= base_url('public/assets/system_images/ipms-icon.png') ?>" alt="IPMS" height="40">
                </div>
                <h2 class="fw-bold mb-2" style="color: #28a745;">Welcome Back</h2>
                <p class="text-muted mb-0">Sign in to your IPMS account</p>
            </div>

            <!-- Login Body -->
            <div class="login-body">
                <!-- Error Alert (Hidden by default) -->
                <div class="alert alert-danger d-none" id="errorAlert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorMessage">Invalid credentials. Please try again.</span>
                </div>

                <!-- Success Alert (Hidden by default) -->
                <div class="alert alert-success d-none" id="successAlert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span id="successMessage">Login successful! Redirecting...</span>
                </div>

                <!-- Login Form -->
                <form id="loginForm" method="POST" action="authenticate.php">
                    <!-- Email Field -->
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                    </div>

                    <!-- Password Field -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                    </div>

                    <!-- Remember Me Checkbox -->
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                        <label class="form-check-label" for="rememberMe">
                            Remember me
                        </label>
                    </div>

                    <!-- Login Button -->
                    <button type="submit" class="btn btn-login text-white mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span class="btn-text">Sign In</span>
                    </button>

                    <!-- Forgot Password Link -->
                    <div class="text-center">
                        <a href="#" class="forgot-password" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                            <i class="fas fa-question-circle me-1"></i>Forgot your password?
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 15px;">
                <div class="modal-header" style="background-color: #28a745; color: white; border-radius: 15px 15px 0 0;">
                    <h5 class="modal-title" id="forgotPasswordModalLabel">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <p class="text-muted mb-3">Enter your email address and we'll send you a link to reset your password.</p>
                    <form id="forgotPasswordForm">
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="resetEmail" placeholder="<EMAIL>" required>
                            <label for="resetEmail">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                        </div>
                        <button type="submit" class="btn w-100" style="background-color: #800000; border-color: #800000; color: white;">
                            <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const forgotPasswordForm = document.getElementById('forgotPasswordForm');
            const errorAlert = document.getElementById('errorAlert');
            const successAlert = document.getElementById('successAlert');

            // Handle login form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const submitBtn = this.querySelector('button[type="submit"]');
                const btnText = submitBtn.querySelector('.btn-text');
                const originalText = btnText.textContent;

                // Show loading state
                btnText.innerHTML = '<span class="loading"></span> Signing In...';
                submitBtn.disabled = true;

                // Hide any existing alerts
                errorAlert.classList.add('d-none');
                successAlert.classList.add('d-none');

                // Simulate authentication (replace with actual AJAX call)
                setTimeout(() => {
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;

                    // Demo credentials (replace with actual authentication)
                    if (email === '<EMAIL>' && password === 'admin123') {
                        // Success
                        successAlert.classList.remove('d-none');
                        setTimeout(() => {
                            window.location.href = '<?= site_url('home/dashboard') ?>';
                        }, 1500);
                    } else {
                        // Error
                        errorAlert.classList.remove('d-none');
                        btnText.textContent = originalText;
                        submitBtn.disabled = false;
                    }
                }, 2000);
            });

            // Handle forgot password form
            forgotPasswordForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;

                // Show loading state
                submitBtn.innerHTML = '<span class="loading"></span> Sending...';
                submitBtn.disabled = true;

                // Simulate sending reset email
                setTimeout(() => {
                    alert('Password reset link sent to your email!');
                    bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal')).hide();
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    this.reset();
                }, 2000);
            });

            // Add floating animation to shapes
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.style.animationDelay = `${index * 0.5}s`;
            });

            // Add focus effects to form inputs
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // Add demo credentials hint
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            emailInput.addEventListener('focus', function() {
                if (!this.value) {
                    setTimeout(() => {
                        if (document.activeElement === this) {
                            this.placeholder = 'Demo: <EMAIL>';
                        }
                    }, 1000);
                }
            });

            passwordInput.addEventListener('focus', function() {
                if (!this.value) {
                    setTimeout(() => {
                        if (document.activeElement === this) {
                            this.placeholder = 'Demo: admin123';
                        }
                    }, 1000);
                }
            });

            emailInput.addEventListener('blur', function() {
                this.placeholder = '<EMAIL>';
            });

            passwordInput.addEventListener('blur', function() {
                this.placeholder = 'Password';
            });
        });
    </script>
</body>
</html>
